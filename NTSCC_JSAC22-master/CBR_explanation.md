# 通道带宽比(Channel Band Ratio, CBR)计算详解

## 基本定义

通道带宽比(CBR)是语义通信系统中衡量带宽利用效率的关键指标，它表示每个输入像素所对应的通道使用量。在NTSCC(Nonlinear Transform Source-Channel Coding)项目中，CBR计算与香农容量定理密切相关。

## 计算方法

### 1. 主要数据流的CBR (cbr_y)

主要数据流的CBR计算公式为：

```
cbr_y = channel_usage / num_pixels
```

其中：
- `channel_usage`: 实际使用的通道复数符号数量
- `num_pixels`: 输入图像的总像素数 (H*W*3)

在代码中的实现位于`NTSCC_Hyperprior`类的`forward`方法中：

```python
# 计算实际通道使用量
channel_input = torch.masked_select(s_masked, mask_BCHW)
channel_output, channel_usage = self.channel.forward(channel_input)

# 计算CBR
cbr_y = channel_usage / num_pixels
```

### 2. 侧信息的CBR (cbr_z)

对于侧信息z，CBR通过将bits per pixel (bpp)转换为通道带宽比：

```python
def bpp_snr_to_kdivn(bpp, SNR):
    snr = 10 ** (SNR / 10)
    kdivn = bpp / 3 / np.log2(1 + snr)
    return kdivn
```

这个转换基于香农容量定理：对于给定SNR的AWGN通道，其容量为`C = log2(1 + SNR)`。

- `bpp`: 侧信息的比特率（每像素比特数）
- `SNR`: 通道信噪比(dB)
- `kdivn`: 通道带宽比

### 3. 额外侧信息的CBR (cbr_sideinfo)

当使用多速率编码时，需要额外传输速率选择信息：

```python
# 容量实现通道编码
cbr_sideinfo = np.log2(config.multiple_rate.__len__()) / (16 * 16 * 3) / np.log2(
    1 + 10 ** (net.channel.chan_param / 10))
```

### 4. 总CBR计算

总CBR是各部分CBR的总和：

- 当`use_side_info=False`时：总CBR = cbr_y
- 当`use_side_info=True`时：总CBR = cbr_y + cbr_z + cbr_sideinfo

## CBR的实际计算流程

1. **编码阶段**：
   - JSCCEncoder根据输入的潜在表示x和其概率估计px计算信息熵
   - 通过`symbol_num = torch.sum(hx, dim=1).flatten(0) * eta`计算需要的符号数
   - 使用RateAdaptionEncoder将潜在表示映射到可变长度的通道输入向量

2. **通道传输**：
   - 通道模块计算实际使用的复数符号数量
   - 传输后计算`cbr_y = channel_usage / num_pixels`

3. **侧信息处理**：
   - 对侧信息z计算bpp
   - 通过`bpp_snr_to_kdivn`函数将bpp转换为cbr_z

## 数学原理

CBR计算基于以下信息论原理：

1. 对于AWGN通道，其容量为：C = log₂(1 + SNR) bits/symbol
2. 要传输R bits/pixel的信息，至少需要 R/C symbols/pixel的通道使用量
3. 因此，CBR = R/C = R/log₂(1 + SNR)

对于RGB图像，像素数为H×W×3，所以对于侧信息z：
```
cbr_z = bpp_z / 3 / log₂(1 + SNR)
```

## 代码中的应用示例

在训练和测试阶段，CBR计算如下：

**训练阶段**：
```python
if config.use_side_info:
    cbr_z = bpp_snr_to_kdivn(bpp_z, 10)
    loss = mse_loss_ntscc + mse_loss_ntc + config.train_lambda * (bpp_y * config.eta + cbr_z)
    cbrs.update(cbr_y + cbr_z)
else:
    ntc_loss = mse_loss_ntc + config.train_lambda * (bpp_y + bpp_z)
    loss = ntc_loss + mse_loss_ntscc
    cbrs.update(cbr_y)
```

**测试阶段**：
```python
if config.use_side_info:
    cbr_z = bpp_snr_to_kdivn(bpp_z, 10)
    cbrs.update(cbr_y + cbr_z)
else:
    cbrs.update(cbr_y)

# 额外的侧信息CBR
cbr_sideinfo = np.log2(config.multiple_rate.__len__()) / (16 * 16 * 3) / np.log2(
    1 + 10 ** (net.channel.chan_param / 10))
```

## CBR的意义

CBR反映了通信系统的带宽效率，它表示为了达到特定失真水平（如PSNR）所需的通道带宽比例。CBR越低意味着通信效率越高。在实际应用中，CBR可用于评估和比较不同语义通信方案的性能。

## 具体计算例子

为了更直观地理解CBR的计算，下面给出一个具体的例子：

### 场景设定

假设我们有以下参数：
- 输入图像尺寸：256×256×3（高×宽×通道数）
- 通道信噪比(SNR)：10dB
- 主数据流的比特率(bpp_y)：0.5 bits/pixel
- 侧信息的比特率(bpp_z)：0.05 bits/pixel
- 使用侧信息：True (use_side_info=True)
- 多速率编码选项数量：16种（config.multiple_rate.__len__() = 16）

### 1. 计算主数据流的CBR (cbr_y)

在NTSCC模型中，主数据流的通道使用由JSCCEncoder决定。假设经过RateAdaptionEncoder映射和通道传输后，得到channel_usage = 98304。

```
num_pixels = 256 × 256 × 3 = 196608
cbr_y = channel_usage / num_pixels = 98304 / 196608 = 0.5
```

这意味着平均每个像素使用0.5个通道符号。

### 2. 计算侧信息的CBR (cbr_z)

对于侧信息，使用bpp_snr_to_kdivn函数将bpp转换为CBR：

```
SNR = 10dB -> snr = 10^(10/10) = 10
容量C = log₂(1 + snr) = log₂(11) ≈ 3.459 bits/symbol
cbr_z = bpp_z / 3 / log₂(1 + snr)
     = 0.05 / 3 / 3.459
     ≈ 0.0048
```

这表示侧信息z平均每个像素使用约0.0048个通道符号。

### 3. 计算额外侧信息的CBR (cbr_sideinfo)

对于多速率编码的额外信息：

```
cbr_sideinfo = log₂(16) / (16×16×3) / log₂(1+10)
             = 4 / 768 / 3.459
             ≈ 0.0015
```

### 4. 计算总CBR

```
总CBR = cbr_y + cbr_z + cbr_sideinfo
      = 0.5 + 0.0048 + 0.0015
      ≈ 0.5063
```

### 实际意义解析

这个CBR值表示：
- 对于每个像素，我们平均使用了约0.5063个通道符号
- 考虑到每个通道符号在SNR=10dB时最多可以传输约3.459比特的信息，这意味着我们的编码效率为(0.5+0.05)/(0.5063×3.459) ≈ 32%
- 如果CBR值较低，表示系统可以用更少的通道资源传输相同质量的图像，通信效率更高

### 与传统方法比较

传统的分离式设计（如JPEG2000+LDPC）通常需要更高的CBR来达到相同的重建质量。例如，对于相同的PSNR，传统方法可能需要CBR=0.7或更高，而NTSCC系统可能只需要CBR=0.5左右。

这个例子展示了CBR如何衡量通信系统的带宽效率，以及NTSCC系统如何通过联合优化源编码和通道编码来提高效率。 