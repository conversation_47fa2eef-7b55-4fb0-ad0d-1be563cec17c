# 语义通信方案统一训练指南

## 🎯 目标

本指南旨在确保所有语义通信方案（NTSCC及其改进版本）使用**统一的训练环境**进行公平对比。

## 📊 数据集选择分析

| 数据集 | 图像数量 | 特点 | 优势 | 劣势 |
|--------|----------|------|------|------|
| **COCO2017** | ~118K | 多样化场景，物体丰富 | 语义丰富，标准benchmark | 尺寸不一，需预处理 |
| **DIV2K** | 800+100 | 高分辨率，高质量 | 细节丰富，超分辨率友好 | 数据量小，场景有限 |
| **OpenImages** | ~1.7M | 大规模，多样化 | 数据量大，泛化性好 | 下载困难，处理复杂 |

### 💡 推荐选择: COCO2017

**理由**:
1. **语义丰富度**: 包含丰富的物体和场景，非常适合语义通信研究
2. **数据量适中**: ~118K图像，训练效率高，不会过拟合
3. **标准benchmark**: 广泛使用，便于与其他方法对比
4. **处理简单**: 相比OpenImages更易获取和处理

## ⚙️ 统一训练配置

### 1. 数据集配置

```python
# config.py
train_data_dir = ['data/train2017']  # COCO2017训练集
test_data_dir = ['data/kodak']       # Kodak测试集
```

### 2. 图像尺寸

COCO2017图像尺寸多样化，建议使用**256×256**作为统一训练尺寸:

```python
# config.py
image_dims = (3, 256, 256)  # 所有方案统一使用
```

### 3. 批次大小与学习率

```python
# config.py
batch_size = 10     # 根据GPU内存调整
lr = 1e-4           # 初始学习率
aux_lr = 1e-3       # 辅助优化器学习率
```

### 4. 信道配置

```python
# config.py
channel = {
    "type": 'awgn',           # 信道类型
    'chan_param': 10,         # SNR (dB)
    'modulation': 'qpsk',     # 调制方案
    'symbol_energy': 1.0      # 符号能量
}
```

### 5. 训练周期

为确保公平对比，所有方案使用相同的训练周期:

```bash
# 训练50个epoch
python main.py --phase train --epochs 50
```

## 🚀 快速开始

### 1. 准备COCO2017数据集

```bash
# 检查并配置COCO2017
python setup_coco.py

# 如需下载数据集
python setup_coco.py --download

# 分析图像尺寸分布
python setup_coco.py --analyze
```

### 2. 训练NTSCC基线模型

```bash
# 使用QPSK调制训练
python main.py --phase train --epochs 50
```

### 3. 测试模型性能

```bash
# 在Kodak数据集上测试
python main.py --phase test --checkpoint path_to_checkpoint
```

## 📈 性能评估指标

为确保公平对比，所有方案使用相同的评估指标:

1. **PSNR**: 峰值信噪比，衡量重建质量
2. **MS-SSIM**: 多尺度结构相似度，衡量感知质量
3. **CBR**: 信道带宽比，衡量传输效率
4. **BPP**: 每像素比特数，衡量压缩效率

## 🔄 方案对比流程

1. **基线训练**: 使用COCO2017训练NTSCC基线模型
2. **改进实现**: 在相同框架下实现数字调制等改进
3. **统一训练**: 使用相同配置训练所有方案
4. **公平测试**: 在Kodak数据集上进行测试
5. **结果对比**: 绘制RD曲线，计算BD-PSNR/BD-Rate

## 📋 实验记录模板

```
实验ID: EXP001
方案名称: NTSCC-QPSK
数据集: COCO2017 (train) / Kodak (test)
训练配置:
- 图像尺寸: 256×256
- 批次大小: 10
- 学习率: 1e-4
- 训练周期: 50 epochs
信道配置:
- 类型: AWGN
- SNR: 10dB
- 调制: QPSK
性能结果:
- PSNR: XX.XX dB
- MS-SSIM: 0.XXX
- CBR: 0.XXX
- BPP: 0.XXX
```

## 🔍 常见问题

### Q: 为什么选择256×256的训练尺寸?
**A**: 这是平衡训练效率和图像质量的折中选择。COCO2017图像尺寸多样，256×256能覆盖大部分图像内容，同时保持训练效率。

### Q: 如何确保不同方案的公平对比?
**A**: 使用相同的数据集、训练配置、评估指标和测试流程。唯一变量应该是方法本身的差异。

### Q: 训练数据集和测试数据集为什么不同?
**A**: 这是机器学习的标准做法。COCO2017用于训练以获得泛化能力，Kodak作为标准测试集评估性能。

### Q: 如何处理COCO2017的多样化尺寸?
**A**: 我们的数据加载器已优化，使用智能调整和随机裁剪确保所有训练样本为256×256。

## 📚 参考资料

1. NTSCC原论文: "Nonlinear Transform Source-Channel Coding for Semantic Communications"
2. COCO2017数据集: https://cocodataset.org/
3. Kodak测试集: http://r0k.us/graphics/kodak/

---

**使用模型**: Claude Sonnet 4
