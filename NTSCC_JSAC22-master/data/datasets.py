import os
import numpy as np
from glob import glob
from torchvision import transforms
from torch.utils.data.dataset import Dataset
from PIL import Image
import torch


class Datasets(Dataset):
    def __init__(self, config, train=False):
        if train:
            self.data_dir = config.train_data_dir
            _, self.im_height, self.im_width = config.image_dims

            # 智能数据增强策略 - 适配COCO2017多样化尺寸
            transforms_list = [
                transforms.Resize((int(self.im_height * 1.2), int(self.im_width * 1.2))),  # 先放大
                transforms.RandomCrop((self.im_height, self.im_width)),  # 随机裁剪到目标尺寸
                transforms.RandomHorizontalFlip(p=0.5),  # 随机水平翻转
                transforms.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1, hue=0.05),  # 颜色抖动
                transforms.ToTensor()]
            self.transform = transforms.Compose(transforms_list)
            self.is_train = True
        else:
            self.data_dir = config.test_data_dir
            # 测试时保持原始处理方式（Kodak数据集）
            self.transform = transforms.Compose([
                transforms.ToTensor()])
            self.is_train = False

        self.imgs = []
        for dir in self.data_dir:
            self.imgs += glob(os.path.join(dir, '*.jpg'))
            self.imgs += glob(os.path.join(dir, '*.png'))
        self.imgs.sort()

        print(f"📊 数据集加载完成: {'训练' if train else '测试'}集 {len(self.imgs)} 张图像")

    def __getitem__(self, item):
        image_ori = self.imgs[item]
        try:
            image = Image.open(image_ori).convert('RGB')

            # 处理尺寸过小的图像
            if self.is_train and (image.width < self.im_width or image.height < self.im_height):
                # 如果图像太小，使用双线性插值放大到目标尺寸
                image = transforms.Resize((max(self.im_height, image.height),
                                          max(self.im_width, image.width)),
                                         interpolation=transforms.InterpolationMode.BILINEAR)(image)

            img = self.transform(image)
            return img

        except Exception as e:
            # 处理损坏的图像
            print(f"⚠️ 警告: 图像 {image_ori} 加载失败: {e}")
            # 返回随机噪声图像作为替代
            return torch.rand(3, self.im_height, self.im_width) if self.is_train else torch.rand(3, 512, 768)

    def __len__(self):
        return len(self.imgs)


def get_loader(config):
    train_dataset = Datasets(config, train=True)
    test_dataset = Datasets(config)

    def worker_init_fn_seed(worker_id):
        seed = 10
        seed += worker_id
        np.random.seed(seed)

    train_loader = torch.utils.data.DataLoader(dataset=train_dataset,
                                               num_workers=config.num_workers,
                                               pin_memory=True,
                                               batch_size=config.batch_size,
                                               worker_init_fn=worker_init_fn_seed,
                                               shuffle=True)

    test_loader = torch.utils.data.DataLoader(dataset=test_dataset,
                                              batch_size=1,
                                              shuffle=False)

    return train_loader, test_loader

def get_test_loader(config):
    test_dataset = Datasets(config)
    test_loader = torch.utils.data.DataLoader(dataset=test_dataset,
                                              batch_size=1,
                                              shuffle=False)

    return test_loader


def get_test_loader(config):
    test_dataset = Datasets(config)
    test_loader = torch.utils.data.DataLoader(dataset=test_dataset,
                                              batch_size=1,
                                              shuffle=False)
    return test_loader
