#!/usr/bin/env python3
"""
详细分析自适应编码的数据处理机制
展示数据是如何被处理的：改变结构 vs 置零
"""

import torch
import numpy as np

def analyze_adaptive_encoding_mechanism():
    """
    分析自适应编码的具体数据处理过程
    """
    print("🔍 自适应编码数据处理机制分析")
    print("=" * 60)
    
    # 模拟RateAdaptionEncoder的输入
    B, C, H, W = 1, 256, 2, 2  # 简化为2x2便于观察
    rate_choice = [16, 32, 48, 64, 80, 96, 102, 118, 134, 160, 186, 192, 208, 224, 240, 256]
    max_rate = max(rate_choice)
    
    print(f"📋 输入维度: B={B}, C={C}, H={H}, W={W}")
    print(f"📋 rate_choice: {rate_choice}")
    print(f"📋 max_rate: {max_rate}")
    print()
    
    # 创建模拟输入数据
    torch.manual_seed(42)
    x = torch.randn(B, C, H, W)
    print("🎯 步骤1: 原始输入数据")
    print("-" * 40)
    print(f"原始数据形状: {x.shape}")
    print(f"原始数据示例 (前4维):")
    for i in range(H):
        for j in range(W):
            print(f"  位置({i},{j}): {x[0, :4, i, j].tolist()}")
    print()
    
    # 模拟不同位置的速率选择
    indexes = torch.tensor([[0, 1], [2, 3]])  # 不同位置选择不同速率
    print("🎯 步骤2: 速率选择")
    print("-" * 40)
    print("速率选择索引:")
    for i in range(H):
        for j in range(W):
            idx = indexes[i, j].item()
            rate = rate_choice[idx]
            print(f"  位置({i},{j}): index={idx} → rate={rate}")
    print()
    
    # 步骤3: 数据重排和线性变换
    print("🎯 步骤3: 数据重排和线性变换")
    print("-" * 40)
    
    # x.flatten(2).permute(0, 2, 1) 的作用
    x_BLC = x.flatten(2).permute(0, 2, 1)  # B, H*W, C
    print(f"重排后形状: {x_BLC.shape} (B, H*W, C)")
    print("重排后数据结构:")
    for pos in range(H*W):
        i, j = pos // W, pos % W
        print(f"  位置{pos}({i},{j}): 前4维 = {x_BLC[0, pos, :4].tolist()}")
    print()
    
    # 步骤4: 权重和偏置选择
    print("🎯 步骤4: 自适应权重和偏置")
    print("-" * 40)
    
    # 模拟权重和偏置（简化）
    torch.manual_seed(123)
    weight = torch.randn(len(rate_choice), C, max_rate) * 0.1  # 缩小权重便于观察
    bias = torch.randn(len(rate_choice), max_rate) * 0.1
    
    # 根据indexes选择对应的权重和偏置
    indexes_flat = indexes.flatten()  # [0, 1, 2, 3]
    w = torch.index_select(weight, 0, indexes_flat).reshape(B, H * W, C, -1)
    b = torch.index_select(bias, 0, indexes_flat).reshape(B, H * W, -1)
    
    print(f"权重形状: {w.shape} (B, H*W, C, max_rate)")
    print(f"偏置形状: {b.shape} (B, H*W, max_rate)")
    print()
    
    # 步骤5: 线性变换
    print("🎯 步骤5: 线性变换")
    print("-" * 40)
    
    # 执行线性变换: x_BLC @ w + b
    x_transformed = torch.matmul(x_BLC.unsqueeze(2), w).squeeze() + b  # B, H*W, max_rate
    print(f"变换后形状: {x_transformed.shape} (B, H*W, max_rate)")
    print("变换后数据 (前4维):")
    for pos in range(H*W):
        i, j = pos // W, pos % W
        print(f"  位置{pos}({i},{j}): {x_transformed[0, pos, :4].tolist()}")
    print()
    
    # 步骤6: 关键的Mask机制
    print("🎯 步骤6: Mask机制 - 这里是关键！")
    print("-" * 40)
    
    # 生成mask
    mask = torch.arange(0, max_rate).repeat(H * W, 1)  # shape: (H*W, max_rate)
    rate_constraint = torch.tensor([rate_choice[idx] for idx in indexes_flat]).unsqueeze(1).repeat(1, max_rate)
    
    print("Mask生成过程:")
    print(f"mask形状: {mask.shape}")
    print(f"rate_constraint形状: {rate_constraint.shape}")
    print()
    
    # 生成新的mask
    mask_new = torch.zeros_like(mask)
    mask_new[mask < rate_constraint] = 1
    mask_new[mask >= rate_constraint] = 0
    
    print("每个位置的mask情况:")
    for pos in range(H*W):
        i, j = pos // W, pos % W
        idx = indexes_flat[pos].item()
        rate = rate_choice[idx]
        kept_dims = mask_new[pos].sum().item()
        
        print(f"  位置{pos}({i},{j}): rate={rate} → 保留前{kept_dims}维, 后{max_rate-kept_dims}维置零")
        print(f"    mask: {mask_new[pos][:8].tolist()}...{mask_new[pos][-4:].tolist()}")
    print()
    
    # 步骤7: 应用mask - 关键操作
    print("🎯 步骤7: 应用Mask - 数据置零操作")
    print("-" * 40)
    
    x_BLC_masked = x_transformed * mask_new
    
    print("Mask应用前后对比:")
    for pos in range(H*W):
        i, j = pos // W, pos % W
        idx = indexes_flat[pos].item()
        rate = rate_choice[idx]
        
        print(f"位置{pos}({i},{j}) - rate={rate}:")
        print(f"  应用前: {x_transformed[0, pos, :8].tolist()}...")
        print(f"  应用后: {x_BLC_masked[0, pos, :8].tolist()}...")
        print(f"  零值数量: {(x_BLC_masked[0, pos] == 0).sum().item()}/{max_rate}")
        print()
    
    # 步骤8: 数据结构恢复
    print("🎯 步骤8: 数据结构恢复")
    print("-" * 40)
    
    x_masked = x_BLC_masked.reshape(B, H, W, -1).permute(0, 3, 1, 2)
    mask_BCHW = mask_new.reshape(B, H, W, -1).permute(0, 3, 1, 2)
    
    print(f"最终输出形状: {x_masked.shape}")
    print(f"最终mask形状: {mask_BCHW.shape}")
    print()
    
    # 分析数据变化
    print("🎯 数据变化分析")
    print("-" * 40)
    
    original_nonzero = (x != 0).sum().item()
    final_nonzero = (x_masked != 0).sum().item()
    total_elements = x.numel()
    
    print(f"原始非零元素: {original_nonzero}/{total_elements}")
    print(f"最终非零元素: {final_nonzero}/{total_elements}")
    print(f"数据保留率: {final_nonzero/total_elements*100:.1f}%")
    print()
    
    print("🎯 关键结论")
    print("-" * 40)
    print("1. 自适应编码 = 线性变换 + 选择性置零")
    print("2. 数据结构没有改变，仍然是 B×C×H×W")
    print("3. 不需要的数据被置零，而不是物理删除")
    print("4. 通过mask机制实现不同位置的不同保留率")
    print("5. 这样设计保持了张量形状的一致性，便于后续处理")

def analyze_channel_processing():
    """
    分析Channel模块的数据处理
    """
    print("\n" + "=" * 60)
    print("🔍 Channel模块数据处理分析")
    print("=" * 60)
    
    # 模拟masked数据进入channel
    print("🎯 Channel模块的数据处理:")
    print("-" * 40)
    
    # 模拟输入：包含很多零的数据
    B, C, H, W = 1, 256, 2, 2
    x_masked = torch.randn(B, C, H, W)
    
    # 模拟mask效果：大部分数据为零
    mask = torch.zeros_like(x_masked)
    mask[:, :16, :, :] = 1  # 只保留前16个通道
    x_masked = x_masked * mask
    
    print(f"输入到Channel的数据形状: {x_masked.shape}")
    print(f"非零元素数量: {(x_masked != 0).sum().item()}")
    print(f"零元素数量: {(x_masked == 0).sum().item()}")
    print()
    
    print("🎯 torch.masked_select的作用:")
    print("-" * 40)
    
    # 模拟NTSCC_Hyperprior中的操作
    mask_BCHW = (x_masked != 0)
    channel_input = torch.masked_select(x_masked, mask_BCHW)
    
    print(f"masked_select前: {x_masked.shape} ({x_masked.numel()}个元素)")
    print(f"masked_select后: {channel_input.shape} ({channel_input.numel()}个元素)")
    print(f"压缩比: {x_masked.numel() / channel_input.numel():.1f}:1")
    print()
    
    print("🎯 关键发现:")
    print("-" * 40)
    print("1. masked_select真正改变了数据结构")
    print("2. 将稀疏的张量压缩为密集的一维向量")
    print("3. 只传输非零元素，大幅减少通道使用")
    print("4. 这是实现高压缩比的关键步骤")

if __name__ == "__main__":
    analyze_adaptive_encoding_mechanism()
    analyze_channel_processing()
