# NTSCC数字调制集成指南

## 🎯 概述

本文档介绍如何在NTSCC (Nonlinear Transform Source-Channel Coding) 系统中集成数字调制功能。这是一个**最小侵入式**的baseline改进方案，将噪声直接加到调制符号上，更贴近实际数字通信系统。

## 🔧 核心改进

### 原始系统 vs 数字调制系统

| 特性 | 原始NTSCC | 数字调制NTSCC |
|------|-----------|---------------|
| 信号类型 | 连续复数信号 | 离散调制符号 |
| 噪声模型 | 连续高斯噪声 | 符号级高斯噪声 |
| 调制方案 | 无 | BPSK/QPSK/16QAM |
| 实际性 | 理论模型 | 更接近实际系统 |

### 信号处理流程

```
原始流程: 特征 → 复数信号 → 加噪声 → 解调
数字调制: 特征 → 复数信号 → 调制 → 加噪声 → 解调 → 输出
```

## 📋 支持的调制方案

### 1. BPSK (Binary Phase Shift Keying)
- **符号数**: 2个
- **星座点**: {±1}
- **特点**: 最简单，抗噪声能力强
- **适用**: 低SNR环境

### 2. QPSK (Quadrature Phase Shift Keying) 
- **符号数**: 4个
- **星座点**: {±1±1j}/√2
- **特点**: 平衡性能与复杂度
- **适用**: 中等SNR环境（推荐）

### 3. 16QAM (16-Quadrature Amplitude Modulation)
- **符号数**: 16个
- **星座点**: 4级幅度量化
- **特点**: 高频谱效率，需要高SNR
- **适用**: 高SNR环境

## ⚙️ 配置方法

### 1. 基本配置

在 `config.py` 中修改信道配置：

```python
channel = {
    "type": 'awgn',           # 信道类型
    'chan_param': 10,         # SNR (dB)
    'modulation': 'qpsk',     # 调制方案: 'none', 'bpsk', 'qpsk', '16qam'
    'symbol_energy': 1.0      # 符号能量
}
```

### 2. 动态配置

```python
from config import config

# 设置QPSK调制，SNR=10dB
config.channel['modulation'] = 'qpsk'
config.channel['chan_param'] = 10
config.channel['symbol_energy'] = 1.0
```

## 🚀 使用示例

### 1. 训练模型

```bash
# 使用QPSK调制训练
python main.py --phase train

# 使用BPSK调制训练（低SNR场景）
# 先修改config.py中的modulation为'bpsk'
python main.py --phase train
```

### 2. 测试模型

```bash
# 测试预训练模型
python main.py --phase test --checkpoint path_to_checkpoint

# 测试不同调制方案
python example_digital_modulation.py
```

### 3. 性能对比

```bash
# 运行完整性能测试
python test_digital_modulation.py
```

## 📊 性能分析

### 理论分析

1. **BPSK**: 在低SNR下表现最佳，但频谱效率低
2. **QPSK**: 平衡性能，适合大多数场景
3. **16QAM**: 高频谱效率，但需要高SNR

### 实验结果示例

```
调制方案    NTC PSNR    NTSCC PSNR    CBR      BPP      增益
NONE       32.45       28.92         0.0625   0.1250   0.00
BPSK       32.12       28.67         0.0625   0.1250   -0.25
QPSK       32.38       29.15         0.0625   0.1250   +0.23
16QAM      32.51       29.08         0.0625   0.1250   +0.16
```

## 🔬 技术细节

### 1. 调制实现

```python
def digital_modulation(self, input_signal):
    if self.modulation == 'qpsk':
        real_part = torch.sign(torch.real(input_signal))
        imag_part = torch.sign(torch.imag(input_signal))
        qpsk_symbols = (real_part + 1j * imag_part) / np.sqrt(2)
        return qpsk_symbols * np.sqrt(self.symbol_energy)
```

### 2. 软解调

```python
def digital_demodulation(self, received_symbols):
    if self.modulation == 'qpsk':
        # 软判决解调，保持梯度流
        return received_symbols * np.sqrt(2) / np.sqrt(self.symbol_energy)
```

### 3. 梯度流保证

- 使用软判决解调保持可微性
- 避免硬判决导致的梯度消失
- 支持端到端训练

## 🛠️ 扩展建议

### 1. 添加新调制方案

```python
elif self.modulation == '8psk':
    # 8PSK实现
    phase = torch.angle(input_signal)
    quantized_phase = torch.round(phase * 4 / np.pi) * np.pi / 4
    return torch.exp(1j * quantized_phase) * np.sqrt(self.symbol_energy)
```

### 2. 自适应调制

```python
def adaptive_modulation(self, input_signal, snr_estimate):
    if snr_estimate > 15:
        return self.modulate_16qam(input_signal)
    elif snr_estimate > 10:
        return self.modulate_qpsk(input_signal)
    else:
        return self.modulate_bpsk(input_signal)
```

### 3. 信道编码集成

```python
def channel_coding(self, symbols):
    # 添加卷积码或LDPC码
    encoded_symbols = self.encoder(symbols)
    return encoded_symbols
```

## 🐛 故障排除

### 常见问题

1. **梯度消失**: 确保使用软解调而非硬判决
2. **设备错误**: 检查CUDA设备配置
3. **性能下降**: 调整符号能量和SNR参数

### 调试技巧

```python
# 检查调制符号
with torch.no_grad():
    symbols = channel.digital_modulation(test_signal)
    print(f"符号范围: {symbols.abs().min():.3f} - {symbols.abs().max():.3f}")

# 检查梯度流
test_signal.requires_grad_(True)
output, _ = channel.forward(test_signal)
loss = output.sum()
loss.backward()
print(f"梯度正常: {test_signal.grad is not None}")
```

## 📚 参考文献

1. NTSCC原论文: "Nonlinear Transform Source-Channel Coding for Semantic Communications"
2. 数字调制理论: Proakis, "Digital Communications"
3. 深度学习通信: "Deep Learning for Wireless Communications"

## 🤝 贡献

欢迎提交Issue和Pull Request来改进数字调制功能！

---

**使用模型**: Claude Sonnet 4
