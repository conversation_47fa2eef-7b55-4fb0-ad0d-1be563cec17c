import torch
import datetime
import torch.nn as nn


class config:

    train_data_dir = ['data/train2017']
    test_data_dir = ['data/kodak']
    batch_size = 10
    num_workers = 8

    print_step = 50
    plot_step = 1000
    logger = None

    # training details
    image_dims = (3, 256, 256)
    lr = 1e-4
    aux_lr = 1e-3
    distortion_metric = 'MSE'  # 'MS-SSIM'

    use_side_info = False
    train_lambda = 64
    eta = 0.2

    # 增强的信道配置 - 集成Image JSCC的信道参数
    channel = {
        "type": 'awgn',
        'chan_param': 10,
        'modulation': 'qpsk',  # 'none', 'qpsk', 'bpsk', '16qam'
        'symbol_energy': 1.0,   # 符号能量

        # OFDM/信道参数 (来自Image JSCC)
        'num_ofdm_symbols': 1,
        'fft_size': 64,
        'num_subcarriers': 64,
        'subcarrier_spacing': 15e3,  # Hz
        'carrier_freq': 3.5e9,       # Hz
        'delay_spread': 100e-9,      # 秒
        'cp_length': 16,
        'num_bits_per_symbol': 2,    # QPSK

        # 量化参数 (来自Image JSCC)
        'quantization_bits': 6,      # 量化比特数
        'enable_quantization': True, # 是否启用量化
        'quantization_method': 'uniform',  # 量化方法
    }
    multiple_rate = [16, 32, 48, 64, 80, 96, 102, 118, 134, 160, 186, 192, 208, 224, 240, 256]
    ga_kwargs = dict(
        img_size=(image_dims[1], image_dims[2]),
        embed_dims=[256, 256, 256, 256], depths=[1, 1, 2, 4], num_heads=[8, 8, 8, 8],
        window_size=8, mlp_ratio=4., qkv_bias=True, qk_scale=None,
        norm_layer=nn.LayerNorm, patch_norm=True,
    )

    gs_kwargs = dict(
        img_size=(image_dims[1], image_dims[2]),
        embed_dims=[256, 256, 256, 256], depths=[4, 2, 1, 1], num_heads=[8, 8, 8, 8],
        window_size=8, mlp_ratio=4., norm_layer=nn.LayerNorm, patch_norm=True
    )

    fe_kwargs = dict(
        input_resolution=(image_dims[1] // 16, image_dims[2] // 16),
        embed_dim=256, depths=[4], num_heads=[8],
        window_size=16, mlp_ratio=4., qkv_bias=True, qk_scale=None,
        norm_layer=nn.LayerNorm, rate_choice=multiple_rate
    )

    fd_kwargs = dict(
        input_resolution=(image_dims[1] // 16, image_dims[2] // 16),
        embed_dim=256, depths=[4], num_heads=[8],
        window_size=16, mlp_ratio=4., qkv_bias=True, qk_scale=None,
        norm_layer=nn.LayerNorm, rate_choice=multiple_rate
    )
