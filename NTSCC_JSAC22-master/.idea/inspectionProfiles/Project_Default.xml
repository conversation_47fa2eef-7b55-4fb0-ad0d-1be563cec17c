<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="115">
            <item index="0" class="java.lang.String" itemvalue="Babel" />
            <item index="1" class="java.lang.String" itemvalue="testpath" />
            <item index="2" class="java.lang.String" itemvalue="defusedxml" />
            <item index="3" class="java.lang.String" itemvalue="py" />
            <item index="4" class="java.lang.String" itemvalue="pycparser" />
            <item index="5" class="java.lang.String" itemvalue="torchvision" />
            <item index="6" class="java.lang.String" itemvalue="ipython-genutils" />
            <item index="7" class="java.lang.String" itemvalue="Pygments" />
            <item index="8" class="java.lang.String" itemvalue="mccabe" />
            <item index="9" class="java.lang.String" itemvalue="bleach" />
            <item index="10" class="java.lang.String" itemvalue="sphinxcontrib-qthelp" />
            <item index="11" class="java.lang.String" itemvalue="docutils" />
            <item index="12" class="java.lang.String" itemvalue="soupsieve" />
            <item index="13" class="java.lang.String" itemvalue="jsonschema" />
            <item index="14" class="java.lang.String" itemvalue="qtconsole" />
            <item index="15" class="java.lang.String" itemvalue="terminado" />
            <item index="16" class="java.lang.String" itemvalue="typing-extensions" />
            <item index="17" class="java.lang.String" itemvalue="jupyter-client" />
            <item index="18" class="java.lang.String" itemvalue="jupyterlab-pygments" />
            <item index="19" class="java.lang.String" itemvalue="click" />
            <item index="20" class="java.lang.String" itemvalue="ipykernel" />
            <item index="21" class="java.lang.String" itemvalue="nbconvert" />
            <item index="22" class="java.lang.String" itemvalue="attrs" />
            <item index="23" class="java.lang.String" itemvalue="jedi" />
            <item index="24" class="java.lang.String" itemvalue="regex" />
            <item index="25" class="java.lang.String" itemvalue="matplotlib" />
            <item index="26" class="java.lang.String" itemvalue="idna" />
            <item index="27" class="java.lang.String" itemvalue="decorator" />
            <item index="28" class="java.lang.String" itemvalue="pluggy" />
            <item index="29" class="java.lang.String" itemvalue="cffi" />
            <item index="30" class="java.lang.String" itemvalue="pandocfilters" />
            <item index="31" class="java.lang.String" itemvalue="pycodestyle" />
            <item index="32" class="java.lang.String" itemvalue="numpy" />
            <item index="33" class="java.lang.String" itemvalue="requests" />
            <item index="34" class="java.lang.String" itemvalue="pyrsistent" />
            <item index="35" class="java.lang.String" itemvalue="sphinxcontrib-devhelp" />
            <item index="36" class="java.lang.String" itemvalue="alabaster" />
            <item index="37" class="java.lang.String" itemvalue="jupyter" />
            <item index="38" class="java.lang.String" itemvalue="nest-asyncio" />
            <item index="39" class="java.lang.String" itemvalue="prompt-toolkit" />
            <item index="40" class="java.lang.String" itemvalue="flake8-comprehensions" />
            <item index="41" class="java.lang.String" itemvalue="ipywidgets" />
            <item index="42" class="java.lang.String" itemvalue="scipy" />
            <item index="43" class="java.lang.String" itemvalue="pyupgrade" />
            <item index="44" class="java.lang.String" itemvalue="black" />
            <item index="45" class="java.lang.String" itemvalue="Send2Trash" />
            <item index="46" class="java.lang.String" itemvalue="imagesize" />
            <item index="47" class="java.lang.String" itemvalue="torch" />
            <item index="48" class="java.lang.String" itemvalue="toml" />
            <item index="49" class="java.lang.String" itemvalue="mistune" />
            <item index="50" class="java.lang.String" itemvalue="Sphinx" />
            <item index="51" class="java.lang.String" itemvalue="jupyter-console" />
            <item index="52" class="java.lang.String" itemvalue="argon2-cffi" />
            <item index="53" class="java.lang.String" itemvalue="isort" />
            <item index="54" class="java.lang.String" itemvalue="pytz" />
            <item index="55" class="java.lang.String" itemvalue="furo" />
            <item index="56" class="java.lang.String" itemvalue="tokenize-rt" />
            <item index="57" class="java.lang.String" itemvalue="webencodings" />
            <item index="58" class="java.lang.String" itemvalue="Pillow" />
            <item index="59" class="java.lang.String" itemvalue="sphinxcontrib-applehelp" />
            <item index="60" class="java.lang.String" itemvalue="traitlets" />
            <item index="61" class="java.lang.String" itemvalue="typed-ast" />
            <item index="62" class="java.lang.String" itemvalue="mypy" />
            <item index="63" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="64" class="java.lang.String" itemvalue="nbclient" />
            <item index="65" class="java.lang.String" itemvalue="QtPy" />
            <item index="66" class="java.lang.String" itemvalue="astroid" />
            <item index="67" class="java.lang.String" itemvalue="cycler" />
            <item index="68" class="java.lang.String" itemvalue="flake8-bugbear" />
            <item index="69" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="70" class="java.lang.String" itemvalue="snowballstemmer" />
            <item index="71" class="java.lang.String" itemvalue="jupyterlab-widgets" />
            <item index="72" class="java.lang.String" itemvalue="pyzmq" />
            <item index="73" class="java.lang.String" itemvalue="certifi" />
            <item index="74" class="java.lang.String" itemvalue="pyparsing" />
            <item index="75" class="java.lang.String" itemvalue="notebook" />
            <item index="76" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="77" class="java.lang.String" itemvalue="iniconfig" />
            <item index="78" class="java.lang.String" itemvalue="wrapt" />
            <item index="79" class="java.lang.String" itemvalue="sphinxcontrib-htmlhelp" />
            <item index="80" class="java.lang.String" itemvalue="appnope" />
            <item index="81" class="java.lang.String" itemvalue="widgetsnbextension" />
            <item index="82" class="java.lang.String" itemvalue="async-generator" />
            <item index="83" class="java.lang.String" itemvalue="pathspec" />
            <item index="84" class="java.lang.String" itemvalue="jupyter-core" />
            <item index="85" class="java.lang.String" itemvalue="mypy-extensions" />
            <item index="86" class="java.lang.String" itemvalue="Jinja2" />
            <item index="87" class="java.lang.String" itemvalue="flake8" />
            <item index="88" class="java.lang.String" itemvalue="pytest-cov" />
            <item index="89" class="java.lang.String" itemvalue="urllib3" />
            <item index="90" class="java.lang.String" itemvalue="coverage" />
            <item index="91" class="java.lang.String" itemvalue="six" />
            <item index="92" class="java.lang.String" itemvalue="pyflakes" />
            <item index="93" class="java.lang.String" itemvalue="parso" />
            <item index="94" class="java.lang.String" itemvalue="pytest" />
            <item index="95" class="java.lang.String" itemvalue="sphinxcontrib-serializinghtml" />
            <item index="96" class="java.lang.String" itemvalue="nbformat" />
            <item index="97" class="java.lang.String" itemvalue="ipython" />
            <item index="98" class="java.lang.String" itemvalue="packaging" />
            <item index="99" class="java.lang.String" itemvalue="prometheus-client" />
            <item index="100" class="java.lang.String" itemvalue="pybind11" />
            <item index="101" class="java.lang.String" itemvalue="chardet" />
            <item index="102" class="java.lang.String" itemvalue="lazy-object-proxy" />
            <item index="103" class="java.lang.String" itemvalue="sphinxcontrib-jsmath" />
            <item index="104" class="java.lang.String" itemvalue="tqdm" />
            <item index="105" class="java.lang.String" itemvalue="appdirs" />
            <item index="106" class="java.lang.String" itemvalue="pytorch-msssim" />
            <item index="107" class="java.lang.String" itemvalue="tornado" />
            <item index="108" class="java.lang.String" itemvalue="backcall" />
            <item index="109" class="java.lang.String" itemvalue="ptyprocess" />
            <item index="110" class="java.lang.String" itemvalue="pickleshare" />
            <item index="111" class="java.lang.String" itemvalue="kiwisolver" />
            <item index="112" class="java.lang.String" itemvalue="wcwidth" />
            <item index="113" class="java.lang.String" itemvalue="entrypoints" />
            <item index="114" class="java.lang.String" itemvalue="pexpect" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>