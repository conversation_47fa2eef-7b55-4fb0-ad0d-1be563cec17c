import torch.nn as nn
import numpy as np
import os
import torch


class Channel(nn.Module):
    def __init__(self, config):
        super(Channel, self).__init__()
        self.config = config
        self.chan_type = config.channel['type']
        self.chan_param = config.channel['chan_param']
        self.device = config.device

        # 数字调制配置
        self.modulation = config.channel.get('modulation', 'none')  # 'none', 'qpsk', 'bpsk', '16qam'
        self.symbol_energy = config.channel.get('symbol_energy', 1.0)

        if config.logger:
            if self.modulation != 'none':
                config.logger.info('【Channel】: Built {} channel with {} modulation, SNR {} dB.'.format(
                    config.channel['type'], self.modulation.upper(), config.channel['chan_param']))
            else:
                config.logger.info('【Channel】: Built {} channel, SNR {} dB.'.format(
                    config.channel['type'], config.channel['chan_param']))

    def gaussian_noise_layer(self, input_layer, std):
        device = input_layer.device if input_layer.device.index is not None else torch.device('cpu')
        noise_real = torch.normal(mean=0.0, std=std, size=np.shape(input_layer), device=device)
        noise_imag = torch.normal(mean=0.0, std=std, size=np.shape(input_layer), device=device)
        noise = noise_real + 1j * noise_imag
        return input_layer + noise

    def digital_modulation(self, input_signal):
        """
        数字调制：将连续信号映射到离散调制符号
        Args:
            input_signal: 复数信号 tensor
        Returns:
            modulated_symbols: 调制后的符号
        """
        if self.modulation == 'none':
            return input_signal

        elif self.modulation == 'qpsk':
            # QPSK调制：将复数信号量化到4个星座点
            real_part = torch.sign(torch.real(input_signal))
            imag_part = torch.sign(torch.imag(input_signal))
            # QPSK星座点：{±1±1j}/√2，归一化能量
            qpsk_symbols = (real_part + 1j * imag_part) / np.sqrt(2) * np.sqrt(self.symbol_energy)
            return qpsk_symbols

        elif self.modulation == 'bpsk':
            # BPSK调制：仅使用实部
            bpsk_symbols = torch.sign(torch.real(input_signal)) * np.sqrt(self.symbol_energy)
            return bpsk_symbols.to(torch.complex64)

        elif self.modulation == '16qam':
            # 16QAM调制：4级幅度量化
            def quantize_4level(x):
                return torch.sign(x) * (1 + 2 * (torch.abs(x) > torch.median(torch.abs(x))))

            real_part = quantize_4level(torch.real(input_signal))
            imag_part = quantize_4level(torch.imag(input_signal))
            # 16QAM归一化
            qam16_symbols = (real_part + 1j * imag_part) / np.sqrt(10) * np.sqrt(self.symbol_energy)
            return qam16_symbols

        else:
            raise ValueError(f"Unsupported modulation: {self.modulation}")

    def digital_demodulation(self, received_symbols):
        """
        数字解调：软判决解调（保持梯度）
        Args:
            received_symbols: 接收到的含噪符号
        Returns:
            demodulated_signal: 解调后的软信号
        """
        if self.modulation == 'none':
            return received_symbols

        elif self.modulation == 'qpsk':
            # QPSK软解调：直接返回接收符号（软判决）
            return received_symbols * np.sqrt(2) / np.sqrt(self.symbol_energy)

        elif self.modulation == 'bpsk':
            # BPSK软解调
            return received_symbols / np.sqrt(self.symbol_energy)

        elif self.modulation == '16qam':
            # 16QAM软解调
            return received_symbols * np.sqrt(10) / np.sqrt(self.symbol_energy)

        else:
            raise ValueError(f"Unsupported demodulation: {self.modulation}")

    def forward(self, input, avg_pwr=None, power=1):
        if avg_pwr is None:
            avg_pwr = torch.mean(input ** 2)
            channel_tx = np.sqrt(power) * input / torch.sqrt(avg_pwr * 2)
        else:
            channel_tx = np.sqrt(power) * input / torch.sqrt(avg_pwr * 2)
        input_shape = channel_tx.shape
        channel_in = channel_tx.reshape(-1)
        channel_in = channel_in[::2] + channel_in[1::2] * 1j

        # 数字调制
        modulated_symbols = self.digital_modulation(channel_in)
        channel_usage = modulated_symbols.numel()

        # 信道传输（在调制符号上加噪声）
        channel_output = self.channel_forward(modulated_symbols)

        # 数字解调
        demodulated_signal = self.digital_demodulation(channel_output)

        # 转换回实数格式
        channel_rx = torch.zeros_like(channel_tx.reshape(-1))
        channel_rx[::2] = torch.real(demodulated_signal)
        channel_rx[1::2] = torch.imag(demodulated_signal)
        channel_rx = channel_rx.reshape(input_shape)
        return channel_rx * torch.sqrt(avg_pwr * 2), channel_usage

    def channel_forward(self, channel_in):
        if self.chan_type == 0 or self.chan_type == 'noiseless':
            return channel_in

        elif self.chan_type == 1 or self.chan_type == 'awgn':
            channel_tx = channel_in
            sigma = np.sqrt(1.0 / (2 * 10 ** (self.chan_param / 10)))
            chan_output = self.gaussian_noise_layer(channel_tx,
                                                    std=sigma)
            return chan_output