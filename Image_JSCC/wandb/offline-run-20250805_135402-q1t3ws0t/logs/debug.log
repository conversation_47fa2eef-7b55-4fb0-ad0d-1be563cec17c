2025-08-05 13:54:02,927 INFO    MainThread:1191823 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-08-05 13:54:02,927 INFO    MainThread:1191823 [wandb_setup.py:_flush():80] Configure stats pid to 1191823
2025-08-05 13:54:02,927 INFO    MainThread:1191823 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-08-05 13:54:02,927 INFO    MainThread:1191823 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/GESCO/Image_JSCC/wandb/settings
2025-08-05 13:54:02,927 INFO    MainThread:1191823 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-05 13:54:02,927 INFO    MainThread:1191823 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /home/<USER>/GESCO/Image_JSCC/wandb/offline-run-20250805_135402-q1t3ws0t/logs/debug.log
2025-08-05 13:54:02,927 INFO    MainThread:1191823 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /home/<USER>/GESCO/Image_JSCC/wandb/offline-run-20250805_135402-q1t3ws0t/logs/debug-internal.log
2025-08-05 13:54:02,927 INFO    MainThread:1191823 [wandb_init.py:init():830] calling init triggers
2025-08-05 13:54:02,927 INFO    MainThread:1191823 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'image_height': 256, 'image_width': 512, 'image_channels': 3, 'batch_size': 4, 'epochs': 300, 'test_interval': 2, 'data_root': '/home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train', 'val_data_root': '/home/<USER>/GESCO/data/Cityscapes/leftImg8bit/val', 'optimizer': 'Adam', 'initial_lr': 5e-05, 'lr_decay_strategy': 'CosineAnnealing', 'lr_decay_epochs': 20, 'exp_decay_rate': 0.9, 'min_lr': 1e-06, 'warmup_epochs': 5, 'gradient_clip_norm': 1.0, 'early_stopping_patience': 20, 'enc_TF_layers': 4, 'dec_TF_layers': 4, 'TF_heads': 8, 'embedding_dim': 256, 'compression_ratio': 2, 'quantization_bits': 6, 'feedback_bits': 16384, 'snr_dB': 10, 'channel_type': 'AWGN', 'num_ofdm_symbols': 1, 'fft_size': 64, 'num_subcarriers': 64, 'subcarrier_spacing': 15000.0, 'carrier_freq': 3500000000.0, 'delay_spread': 1e-07, 'cp_length': 16, 'num_bits_per_symbol': 2, 'currentDT': '20250805_135402', 'root': '/home/<USER>/GESCO/Image_JSCC', 'ckpt_dir': '/home/<USER>/GESCO/Image_JSCC/checkpoints/ImageJSCC_snr_10dB_comp_2_20250805_135402/', 'log_dir': '/home/<USER>/GESCO/Image_JSCC/logs/ImageJSCC_snr_10dB_comp_2_20250805_135402/', 'notes': 'Image JSCC transmission with compression ratio 2, SNR 10dB', '_wandb': {}}
2025-08-05 13:54:02,927 INFO    MainThread:1191823 [wandb_init.py:init():871] starting backend
2025-08-05 13:54:03,131 INFO    MainThread:1191823 [wandb_init.py:init():874] sending inform_init request
2025-08-05 13:54:03,134 INFO    MainThread:1191823 [wandb_init.py:init():882] backend started and connected
2025-08-05 13:54:03,135 INFO    MainThread:1191823 [wandb_init.py:init():953] updated telemetry
2025-08-05 13:54:03,140 INFO    MainThread:1191823 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-08-05 13:54:03,259 INFO    MainThread:1191823 [wandb_init.py:init():1029] starting run threads in backend
2025-08-05 13:54:03,400 INFO    MainThread:1191823 [wandb_run.py:_console_start():2458] atexit reg
2025-08-05 13:54:03,400 INFO    MainThread:1191823 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-08-05 13:54:03,400 INFO    MainThread:1191823 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-08-05 13:54:03,400 INFO    MainThread:1191823 [wandb_run.py:_redirect():2398] Redirects installed.
2025-08-05 13:54:03,401 INFO    MainThread:1191823 [wandb_init.py:init():1075] run started, returning control to user process
