{"os": "Linux-6.8.0-60-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.16", "startedAt": "2025-08-05T04:19:30.481403Z", "program": "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", "codePath": "Image_JSCC/train_image_jscc.py", "codePathLocal": "train_image_jscc.py", "git": {"remote": "https://github.com/ispamm/GESCO.git", "commit": "3008010038d4a6217918e34d0a812e40f26d77eb"}, "email": "<EMAIL>", "root": "/home/<USER>/GESCO/Image_JSCC", "host": "by-System-Product-Name", "executable": "/home/<USER>/anaconda3/envs/GAI/bin/python", "cpu_count": 24, "cpu_count_logical": 48, "gpu": "NVIDIA RTX 6000 Ada Generation", "gpu_count": 2, "disk": {"/": {"total": "************", "used": "51716345856"}}, "memory": {"total": "134540476416"}, "gpu_nvidia": [{"name": "NVIDIA RTX 6000 Ada Generation", "memoryTotal": "51527024640", "cudaCores": 18176, "architecture": "Ada", "uuid": "GPU-a16686db-1278-1c76-a835-b1ef8c7c334d"}, {"name": "NVIDIA RTX 6000 Ada Generation", "memoryTotal": "51527024640", "cudaCores": 18176, "architecture": "Ada", "uuid": "GPU-5f01025c-0fbf-290a-33b9-3aa195cf1ff1"}], "cudaVersion": "12.2", "writerId": "ssk2jxyoyn7rctq9i0ucpgj8zd9apbff"}