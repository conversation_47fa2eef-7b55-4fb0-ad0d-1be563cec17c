2025-08-05 10:56:13,598 INFO    MainThread:1023191 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-08-05 10:56:13,598 INFO    MainThread:1023191 [wandb_setup.py:_flush():80] Configure stats pid to 1023191
2025-08-05 10:56:13,598 INFO    MainThread:1023191 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-08-05 10:56:13,598 INFO    MainThread:1023191 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/GESCO/Image_JSCC/wandb/settings
2025-08-05 10:56:13,598 INFO    MainThread:1023191 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-05 10:56:13,598 INFO    MainThread:1023191 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_105613-zvjqjnit/logs/debug.log
2025-08-05 10:56:13,598 INFO    MainThread:1023191 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_105613-zvjqjnit/logs/debug-internal.log
2025-08-05 10:56:13,598 INFO    MainThread:1023191 [wandb_init.py:init():830] calling init triggers
2025-08-05 10:56:13,598 INFO    MainThread:1023191 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'image_height': 512, 'image_width': 1024, 'batch_size': 2, 'epochs': 300, 'learning_rate': 0.0001, 'compression_ratio': 1, 'quantization_bits': 8, 'snr_dB': 10, 'channel_type': 'AWGN', '_wandb': {}}
2025-08-05 10:56:13,598 INFO    MainThread:1023191 [wandb_init.py:init():871] starting backend
2025-08-05 10:56:13,804 INFO    MainThread:1023191 [wandb_init.py:init():874] sending inform_init request
2025-08-05 10:56:13,806 INFO    MainThread:1023191 [wandb_init.py:init():882] backend started and connected
2025-08-05 10:56:13,807 INFO    MainThread:1023191 [wandb_init.py:init():953] updated telemetry
2025-08-05 10:56:13,812 INFO    MainThread:1023191 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-08-05 10:56:14,856 INFO    MainThread:1023191 [wandb_init.py:init():1029] starting run threads in backend
2025-08-05 10:56:14,936 INFO    MainThread:1023191 [wandb_run.py:_console_start():2458] atexit reg
2025-08-05 10:56:14,936 INFO    MainThread:1023191 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-08-05 10:56:14,936 INFO    MainThread:1023191 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-08-05 10:56:14,936 INFO    MainThread:1023191 [wandb_run.py:_redirect():2398] Redirects installed.
2025-08-05 10:56:14,938 INFO    MainThread:1023191 [wandb_init.py:init():1075] run started, returning control to user process
2025-08-05 10:56:52,786 INFO    MsgRouterThr:1023191 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
