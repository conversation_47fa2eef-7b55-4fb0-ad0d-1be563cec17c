Configuration loaded:
  Image size: 512x1024x3
  Batch size: 2
  Epochs: 300
  Compression ratio: 1
  SNR: 10 dB
  Channel type: AWGN

Loading datasets...
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Found 500 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/val
Steps per epoch: 1487

Creating model...
/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py:421: UserWarning: `build()` was called on layer 'image_encoder', however the layer does not have a `build()` method implemented and it looks like it has unbuilt state. This will cause the layer to be marked as built, despite not being actually built, which may cause failures down the line. Make sure to implement a proper `build()` method.
  warnings.warn(
============================================================
Image JSCC Model Summary
============================================================
Input shape: (512, 1024, 3)
Compression ratio: 1
Quantization bits: 8
Channel type: AWGN
SNR: 10 dB
Compressed features: 8192
Transmitted bits: 65536
Actual compression ratio: 192.00
============================================================
Encoder parameters: 7,041,216
Decoder parameters: 680,030,531
Total trainable parameters: 687,071,747
============================================================

Starting training...
Logs will be saved to: /home/<USER>/GESCO/Image_JSCC/logs/ImageJSCC_snr_10dB_comp_1_20250805_105612/
Checkpoints will be saved to: /home/<USER>/GESCO/Image_JSCC/checkpoints/ImageJSCC_snr_10dB_comp_1_20250805_105612/

Epoch 1/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training:   0%|                                                                      | 0/1487 [00:30<?, ?it/s]
Traceback (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 290, in <module>
    main()
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 194, in main
    loss = train_step(model, optimizer, loss_fn, x_batch)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/util/traceback_utils.py", line 153, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/eager/execute.py", line 53, in quick_execute
    tensors = pywrap_tfe.TFE_Py_Execute(ctx._handle, device_name, op_name,
tensorflow.python.framework.errors_impl.ResourceExhaustedError: Graph execution error:

Detected at node image_jscc_model_1/image_encoder_1/encoder_attention_1_1/encoder_attention_1-Attention_1/truediv_1 defined at (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 290, in <module>

  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 194, in main

  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 47, in train_step

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 117, in error_handler

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py", line 936, in __call__

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 117, in error_handler

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/ops/operation.py", line 58, in __call__

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 156, in error_handler

  File "/home/<USER>/GESCO/Image_JSCC/model/image_jscc_model.py", line 71, in call

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 117, in error_handler

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py", line 936, in __call__

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 117, in error_handler

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/ops/operation.py", line 58, in __call__

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 156, in error_handler

  File "/home/<USER>/GESCO/Image_JSCC/layers/image_encoder.py", line 140, in call

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 117, in error_handler

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py", line 936, in __call__

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 117, in error_handler

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/ops/operation.py", line 58, in __call__

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 156, in error_handler

  File "/home/<USER>/GESCO/Image_JSCC/layers/attention_modules.py", line 211, in call

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 117, in error_handler

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py", line 936, in __call__

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 117, in error_handler

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/ops/operation.py", line 58, in __call__

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 156, in error_handler

  File "/home/<USER>/GESCO/Image_JSCC/layers/attention_modules.py", line 35, in call

failed to allocate memory
	 [[{{node image_jscc_model_1/image_encoder_1/encoder_attention_1_1/encoder_attention_1-Attention_1/truediv_1}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info. This isn't available when running in Eager mode.
 [Op:__inference_train_step_68252]
Traceback (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 290, in <module>
    main()
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 194, in main
    loss = train_step(model, optimizer, loss_fn, x_batch)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/util/traceback_utils.py", line 153, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/eager/execute.py", line 53, in quick_execute
    tensors = pywrap_tfe.TFE_Py_Execute(ctx._handle, device_name, op_name,
tensorflow.python.framework.errors_impl.ResourceExhaustedError: Graph execution error:

Detected at node image_jscc_model_1/image_encoder_1/encoder_attention_1_1/encoder_attention_1-Attention_1/truediv_1 defined at (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 290, in <module>

  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 194, in main

  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 47, in train_step

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 117, in error_handler

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py", line 936, in __call__

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 117, in error_handler

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/ops/operation.py", line 58, in __call__

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 156, in error_handler

  File "/home/<USER>/GESCO/Image_JSCC/model/image_jscc_model.py", line 71, in call

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 117, in error_handler

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py", line 936, in __call__

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 117, in error_handler

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/ops/operation.py", line 58, in __call__

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 156, in error_handler

  File "/home/<USER>/GESCO/Image_JSCC/layers/image_encoder.py", line 140, in call

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 117, in error_handler

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py", line 936, in __call__

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 117, in error_handler

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/ops/operation.py", line 58, in __call__

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 156, in error_handler

  File "/home/<USER>/GESCO/Image_JSCC/layers/attention_modules.py", line 211, in call

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 117, in error_handler

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py", line 936, in __call__

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 117, in error_handler

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/ops/operation.py", line 58, in __call__

  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 156, in error_handler

  File "/home/<USER>/GESCO/Image_JSCC/layers/attention_modules.py", line 35, in call

failed to allocate memory
	 [[{{node image_jscc_model_1/image_encoder_1/encoder_attention_1_1/encoder_attention_1-Attention_1/truediv_1}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info. This isn't available when running in Eager mode.
 [Op:__inference_train_step_68252]
