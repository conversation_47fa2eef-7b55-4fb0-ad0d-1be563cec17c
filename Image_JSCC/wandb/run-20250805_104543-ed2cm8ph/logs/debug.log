2025-08-05 10:45:43,998 INFO    MainThread:1011596 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-08-05 10:45:43,998 INFO    MainThread:1011596 [wandb_setup.py:_flush():80] Configure stats pid to 1011596
2025-08-05 10:45:43,998 INFO    MainThread:1011596 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-08-05 10:45:43,998 INFO    MainThread:1011596 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/GESCO/Image_JSCC/wandb/settings
2025-08-05 10:45:43,998 INFO    MainThread:1011596 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-05 10:45:43,998 INFO    MainThread:1011596 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_104543-ed2cm8ph/logs/debug.log
2025-08-05 10:45:43,998 INFO    MainThread:1011596 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /home/<USER>/GESCO/Image_JSCC/wandb/run-20250805_104543-ed2cm8ph/logs/debug-internal.log
2025-08-05 10:45:43,998 INFO    MainThread:1011596 [wandb_init.py:init():830] calling init triggers
2025-08-05 10:45:43,998 INFO    MainThread:1011596 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'image_height': 512, 'image_width': 1024, 'batch_size': 4, 'epochs': 100, 'learning_rate': 5e-05, 'compression_ratio': 1, 'quantization_bits': 8, 'snr_dB': 10, 'channel_type': 'AWGN', '_wandb': {}}
2025-08-05 10:45:43,998 INFO    MainThread:1011596 [wandb_init.py:init():871] starting backend
2025-08-05 10:45:44,206 INFO    MainThread:1011596 [wandb_init.py:init():874] sending inform_init request
2025-08-05 10:45:44,210 INFO    MainThread:1011596 [wandb_init.py:init():882] backend started and connected
2025-08-05 10:45:44,211 INFO    MainThread:1011596 [wandb_init.py:init():953] updated telemetry
2025-08-05 10:45:44,216 INFO    MainThread:1011596 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-08-05 10:45:45,575 INFO    MainThread:1011596 [wandb_init.py:init():1029] starting run threads in backend
2025-08-05 10:45:45,686 INFO    MainThread:1011596 [wandb_run.py:_console_start():2458] atexit reg
2025-08-05 10:45:45,686 INFO    MainThread:1011596 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-08-05 10:45:45,686 INFO    MainThread:1011596 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-08-05 10:45:45,686 INFO    MainThread:1011596 [wandb_run.py:_redirect():2398] Redirects installed.
2025-08-05 10:45:45,688 INFO    MainThread:1011596 [wandb_init.py:init():1075] run started, returning control to user process
2025-08-05 10:50:38,694 INFO    MsgRouterThr:1011596 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
2025-08-05 10:50:39,279 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,283 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,283 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,283 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,283 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,284 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,284 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,284 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,284 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,284 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,284 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,284 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,284 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,285 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,285 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,286 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,286 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,286 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,286 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,286 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,287 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,287 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,287 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,287 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,287 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,287 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,288 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,288 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,288 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,288 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,288 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,288 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-08-05 10:50:39,288 ERROR   MainThread:1011596 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
