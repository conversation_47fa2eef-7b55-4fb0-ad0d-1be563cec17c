_wandb:
    value:
        cli_version: 0.21.0
        e:
            ok9ccuvu9b64kpk77qzd6cso4gro020n:
                codePath: Image_JSCC/train_image_jscc.py
                codePathLocal: train_image_jscc.py
                cpu_count: 24
                cpu_count_logical: 48
                cudaVersion: "12.2"
                disk:
                    /:
                        total: "200449576960"
                        used: "51714797568"
                email: <EMAIL>
                executable: /home/<USER>/anaconda3/envs/GAI/bin/python
                git:
                    commit: 3008010038d4a6217918e34d0a812e40f26d77eb
                    remote: https://github.com/ispamm/GESCO.git
                gpu: NVIDIA RTX 6000 Ada Generation
                gpu_count: 2
                gpu_nvidia:
                    - architecture: Ada
                      cudaCores: 18176
                      memoryTotal: "51527024640"
                      name: NVIDIA RTX 6000 Ada Generation
                      uuid: GPU-a16686db-1278-1c76-a835-b1ef8c7c334d
                    - architecture: Ada
                      cudaCores: 18176
                      memoryTotal: "51527024640"
                      name: NVIDIA RTX 6000 Ada Generation
                      uuid: GPU-5f01025c-0fbf-290a-33b9-3aa195cf1ff1
                host: by-System-Product-Name
                memory:
                    total: "134540476416"
                os: Linux-6.8.0-60-generic-x86_64-with-glibc2.35
                program: /home/<USER>/GESCO/Image_JSCC/train_image_jscc.py
                python: CPython 3.10.16
                root: /home/<USER>/GESCO/Image_JSCC
                startedAt: "2025-08-05T02:45:43.997226Z"
                writerId: ok9ccuvu9b64kpk77qzd6cso4gro020n
        m: []
        python_version: 3.10.16
        t:
            "1":
                - 2
                - 3
                - 5
                - 49
                - 53
            "2":
                - 2
                - 3
                - 5
                - 49
                - 53
            "3":
                - 13
                - 16
            "4": 3.10.16
            "5": 0.21.0
            "8":
                - 2
            "12": 0.21.0
            "13": linux-x86_64
batch_size:
    value: 4
channel_type:
    value: AWGN
compression_ratio:
    value: 1
epochs:
    value: 100
image_height:
    value: 512
image_width:
    value: 1024
learning_rate:
    value: 5e-05
quantization_bits:
    value: 8
snr_dB:
    value: 10
