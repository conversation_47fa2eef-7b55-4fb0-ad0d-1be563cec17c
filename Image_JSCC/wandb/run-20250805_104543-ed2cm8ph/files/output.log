Configuration loaded:
  Image size: 512x1024x3
  Batch size: 4
  Epochs: 100
  Compression ratio: 1
  SNR: 10 dB
  Channel type: AWGN

Loading datasets...
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Found 500 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/val
Steps per epoch: 743

Creating model...
/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py:421: UserWarning: `build()` was called on layer 'image_encoder', however the layer does not have a `build()` method implemented and it looks like it has unbuilt state. This will cause the layer to be marked as built, despite not being actually built, which may cause failures down the line. Make sure to implement a proper `build()` method.
  warnings.warn(
============================================================
Image JSCC Model Summary
============================================================
Input shape: (512, 1024, 3)
Compression ratio: 1
Quantization bits: 8
Channel type: AWGN
SNR: 10 dB
Compressed features: 8192
Transmitted bits: 65536
Actual compression ratio: 192.00
============================================================
Encoder parameters: 1,919,104
Decoder parameters: 204,130,563
Total trainable parameters: 206,049,667
============================================================

Starting training...
Logs will be saved to: /home/<USER>/GESCO/Image_JSCC/logs/ImageJSCC_snr_10dB_comp_1_20250805_104543/
Checkpoints will be saved to: /home/<USER>/GESCO/Image_JSCC/checkpoints/ImageJSCC_snr_10dB_comp_1_20250805_104543/

Epoch 1/100
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Training:  57%|███████████████████████████▏                    | 421/743 [04:48<03:40,  1.46it/s, loss=0.0395]
Traceback (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 266, in <module>
    # 保存样本图像
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 195, in main
    epoch_loss += loss.numpy()
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/framework/ops.py", line 419, in numpy
    maybe_arr = self._numpy()  # pylint: disable=protected-access
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/framework/ops.py", line 385, in _numpy
    return self._numpy_internal()
KeyboardInterrupt
Traceback (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 266, in <module>
    # 保存样本图像
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 195, in main
    epoch_loss += loss.numpy()
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/framework/ops.py", line 419, in numpy
    maybe_arr = self._numpy()  # pylint: disable=protected-access
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/framework/ops.py", line 385, in _numpy
    return self._numpy_internal()
KeyboardInterrupt
