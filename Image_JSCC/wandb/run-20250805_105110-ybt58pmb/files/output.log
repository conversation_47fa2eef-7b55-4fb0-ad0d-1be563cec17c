Configuration loaded:
  Image size: 1024x2048x3
  Batch size: 4
  Epochs: 100
  Compression ratio: 1
  SNR: 10 dB
  Channel type: AWGN

Loading datasets...
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Found 500 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/val
Steps per epoch: 743

Creating model...
Traceback (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 290, in <module>
    main()
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 159, in main
    model = create_model(config)
  File "/home/<USER>/GESCO/Image_JSCC/model/image_jscc_model.py", line 157, in create_model
    _ = model(dummy_input, training=False)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 122, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "/home/<USER>/GESCO/Image_JSCC/model/image_jscc_model.py", line 71, in call
    encoded_features = self.encoder(inputs, training=training)
  File "/home/<USER>/GESCO/Image_JSCC/layers/image_encoder.py", line 140, in call
    attention_output = layer['attention'](x, training=training)
  File "/home/<USER>/GESCO/Image_JSCC/layers/attention_modules.py", line 126, in build
    raise IndexError('Invalid head number %d with the given input dim %d' % (self.head_num, feature_dim))
IndexError: Exception encountered when calling ImageEncoder.call().

[1mInvalid head number 6 with the given input dim 128[0m

Arguments received by ImageEncoder.call():
  • inputs=tf.Tensor(shape=(1, 1024, 2048, 3), dtype=float32)
  • training=False
Traceback (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 290, in <module>
    main()
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 159, in main
    model = create_model(config)
  File "/home/<USER>/GESCO/Image_JSCC/model/image_jscc_model.py", line 157, in create_model
    _ = model(dummy_input, training=False)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 122, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "/home/<USER>/GESCO/Image_JSCC/model/image_jscc_model.py", line 71, in call
    encoded_features = self.encoder(inputs, training=training)
  File "/home/<USER>/GESCO/Image_JSCC/layers/image_encoder.py", line 140, in call
    attention_output = layer['attention'](x, training=training)
  File "/home/<USER>/GESCO/Image_JSCC/layers/attention_modules.py", line 126, in build
    raise IndexError('Invalid head number %d with the given input dim %d' % (self.head_num, feature_dim))
IndexError: Exception encountered when calling ImageEncoder.call().

[1mInvalid head number 6 with the given input dim 128[0m

Arguments received by ImageEncoder.call():
  • inputs=tf.Tensor(shape=(1, 1024, 2048, 3), dtype=float32)
  • training=False
