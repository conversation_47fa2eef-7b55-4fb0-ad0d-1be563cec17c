#!/usr/bin/env python3
"""
调试重建图像为噪声的问题
"""

import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import os

# 设置GPU内存增长
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
    except RuntimeError as e:
        print(e)

# 设置使用GPU 1
os.environ['CUDA_VISIBLE_DEVICES'] = '1'

from config import ImageJSCCConfig
from model.image_jscc_model import create_model
from data.image_dataset import get_image_dataset
from loss.image_loss import create_loss_function

def debug_model_step_by_step():
    """逐步调试模型各层输出"""
    print("🔍 逐步调试模型各层输出...")
    
    config = ImageJSCCConfig()
    model = create_model(config)
    
    # 获取一个测试样本
    dataset = get_image_dataset(config, 1, dataset_type='train')
    for x_batch in dataset.take(1):
        print(f"输入图像: {x_batch.shape}, 范围: [{tf.reduce_min(x_batch):.4f}, {tf.reduce_max(x_batch):.4f}]")
        
        # 1. 编码器输出
        encoded = model.encoder(x_batch, training=False)
        print(f"编码器输出: {encoded.shape}, 范围: [{tf.reduce_min(encoded):.4f}, {tf.reduce_max(encoded):.4f}]")
        
        # 检查编码器输出是否有异常
        if tf.reduce_any(tf.math.is_nan(encoded)):
            print("❌ 编码器输出包含NaN!")
        if tf.reduce_any(tf.math.is_inf(encoded)):
            print("❌ 编码器输出包含Inf!")
        
        # 2. 量化输出
        quantized = model.quantization(encoded, training=False)
        print(f"量化输出: {quantized.shape}, 范围: [{tf.reduce_min(quantized):.4f}, {tf.reduce_max(quantized):.4f}]")
        
        # 检查量化输出
        if tf.reduce_any(tf.math.is_nan(quantized)):
            print("❌ 量化输出包含NaN!")
        if tf.reduce_any(tf.math.is_inf(quantized)):
            print("❌ 量化输出包含Inf!")
        
        # 3. 信道输出
        received = model.channel(quantized, snr_db=config.snr_dB, training=False)
        print(f"信道输出: {received.shape}, 范围: [{tf.reduce_min(received):.4f}, {tf.reduce_max(received):.4f}]")
        
        # 检查信道输出
        if tf.reduce_any(tf.math.is_nan(received)):
            print("❌ 信道输出包含NaN!")
        if tf.reduce_any(tf.math.is_inf(received)):
            print("❌ 信道输出包含Inf!")
        
        # 4. 反量化输出
        dequantized = model.dequantization(received, training=False)
        print(f"反量化输出: {dequantized.shape}, 范围: [{tf.reduce_min(dequantized):.4f}, {tf.reduce_max(dequantized):.4f}]")
        
        # 检查反量化输出
        if tf.reduce_any(tf.math.is_nan(dequantized)):
            print("❌ 反量化输出包含NaN!")
        if tf.reduce_any(tf.math.is_inf(dequantized)):
            print("❌ 反量化输出包含Inf!")
        
        # 5. 解码器输出
        reconstructed = model.decoder(dequantized, training=False)
        print(f"解码器输出: {reconstructed.shape}, 范围: [{tf.reduce_min(reconstructed):.4f}, {tf.reduce_max(reconstructed):.4f}]")
        
        # 检查解码器输出
        if tf.reduce_any(tf.math.is_nan(reconstructed)):
            print("❌ 解码器输出包含NaN!")
        if tf.reduce_any(tf.math.is_inf(reconstructed)):
            print("❌ 解码器输出包含Inf!")
        
        # 6. 完整模型输出
        full_output = model(x_batch, training=False)
        print(f"完整模型输出: {full_output.shape}, 范围: [{tf.reduce_min(full_output):.4f}, {tf.reduce_max(full_output):.4f}]")
        
        # 检查完整输出
        if tf.reduce_any(tf.math.is_nan(full_output)):
            print("❌ 完整模型输出包含NaN!")
        if tf.reduce_any(tf.math.is_inf(full_output)):
            print("❌ 完整模型输出包含Inf!")
        
        # 可视化对比
        visualize_comparison(x_batch, full_output)
        
        return {
            'input': x_batch,
            'encoded': encoded,
            'quantized': quantized,
            'received': received,
            'dequantized': dequantized,
            'reconstructed': reconstructed,
            'full_output': full_output
        }

def visualize_comparison(original, reconstructed):
    """可视化原图和重建图对比"""
    print("\n📊 生成对比图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 原图
    axes[0, 0].imshow(original[0].numpy())
    axes[0, 0].set_title('Original Image')
    axes[0, 0].axis('off')
    
    # 重建图
    recon_img = np.clip(reconstructed[0].numpy(), 0, 1)
    axes[0, 1].imshow(recon_img)
    axes[0, 1].set_title('Reconstructed Image')
    axes[0, 1].axis('off')
    
    # 差异图
    diff = np.abs(original[0].numpy() - recon_img)
    axes[1, 0].imshow(diff, cmap='hot')
    axes[1, 0].set_title('Absolute Difference')
    axes[1, 0].axis('off')
    
    # 重建图的直方图
    axes[1, 1].hist(recon_img.flatten(), bins=50, alpha=0.7, label='Reconstructed')
    axes[1, 1].hist(original[0].numpy().flatten(), bins=50, alpha=0.7, label='Original')
    axes[1, 1].set_title('Pixel Value Distribution')
    axes[1, 1].legend()
    
    plt.tight_layout()
    plt.savefig('debug_reconstruction_comparison.png', dpi=150, bbox_inches='tight')
    plt.show()
    print("对比图已保存为 'debug_reconstruction_comparison.png'")

def test_simple_autoencoder():
    """测试简化的自编码器"""
    print("\n🧪 测试简化自编码器...")
    
    config = ImageJSCCConfig()
    
    # 创建简化模型（跳过信道）
    encoder = tf.keras.Sequential([
        tf.keras.layers.Conv2D(64, 3, strides=2, padding='same', activation='relu'),
        tf.keras.layers.Conv2D(128, 3, strides=2, padding='same', activation='relu'),
        tf.keras.layers.Conv2D(256, 3, strides=2, padding='same', activation='relu'),
        tf.keras.layers.GlobalAveragePooling2D(),
        tf.keras.layers.Dense(512, activation='relu')
    ])
    
    decoder = tf.keras.Sequential([
        tf.keras.layers.Dense(32*64*256, activation='relu'),
        tf.keras.layers.Reshape([32, 64, 256]),
        tf.keras.layers.Conv2DTranspose(128, 3, strides=2, padding='same', activation='relu'),
        tf.keras.layers.Conv2DTranspose(64, 3, strides=2, padding='same', activation='relu'),
        tf.keras.layers.Conv2DTranspose(32, 3, strides=2, padding='same', activation='relu'),
        tf.keras.layers.Conv2D(3, 3, padding='same', activation='sigmoid')
    ])
    
    # 测试简化模型
    dataset = get_image_dataset(config, 1, dataset_type='train')
    for x_batch in dataset.take(1):
        encoded = encoder(x_batch)
        decoded = decoder(encoded)
        
        print(f"简化编码器输出: {encoded.shape}, 范围: [{tf.reduce_min(encoded):.4f}, {tf.reduce_max(encoded):.4f}]")
        print(f"简化解码器输出: {decoded.shape}, 范围: [{tf.reduce_min(decoded):.4f}, {tf.reduce_max(decoded):.4f}]")
        
        # 计算MSE
        mse = tf.reduce_mean(tf.square(x_batch - decoded))
        psnr = tf.reduce_mean(tf.image.psnr(x_batch, decoded, max_val=1.0))
        
        print(f"简化模型 MSE: {mse:.6f}, PSNR: {psnr:.2f} dB")
        
        break

def check_model_weights():
    """检查模型权重是否正常"""
    print("\n🔍 检查模型权重...")
    
    config = ImageJSCCConfig()
    model = create_model(config)
    
    # 检查各层权重
    for layer in model.layers:
        if hasattr(layer, 'weights') and layer.weights:
            for weight in layer.weights:
                weight_values = weight.numpy()
                print(f"{layer.name} - {weight.name}: 形状={weight.shape}, 范围=[{np.min(weight_values):.4f}, {np.max(weight_values):.4f}]")
                
                # 检查异常值
                if np.any(np.isnan(weight_values)):
                    print(f"  ❌ {weight.name} 包含NaN!")
                if np.any(np.isinf(weight_values)):
                    print(f"  ❌ {weight.name} 包含Inf!")
                if np.all(weight_values == 0):
                    print(f"  ⚠️ {weight.name} 全为零!")

def main():
    """主调试函数"""
    print("🚀 开始调试重建噪声问题...")
    
    # 1. 检查模型权重
    check_model_weights()
    
    # 2. 逐步调试模型
    debug_results = debug_model_step_by_step()
    
    # 3. 测试简化自编码器
    test_simple_autoencoder()
    
    print("\n💡 可能的问题和解决方案:")
    print("1. 如果编码器输出异常 → 检查CNN层配置")
    print("2. 如果量化/反量化异常 → 检查Dense层激活函数")
    print("3. 如果信道输出异常 → 检查信道实现")
    print("4. 如果解码器输出异常 → 检查Transformer和CNN重建层")
    print("5. 如果权重异常 → 重新初始化模型")

if __name__ == "__main__":
    main()
