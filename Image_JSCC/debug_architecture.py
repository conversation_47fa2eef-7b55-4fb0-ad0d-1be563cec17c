#!/usr/bin/env python3
"""
调试网络架构问题
"""

import tensorflow as tf
import numpy as np
import sys
import os

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import ImageJSCCConfig
from model.image_jscc_model import create_model

def debug_feature_dimensions():
    """调试特征维度匹配问题"""
    print("🔍 调试特征维度匹配...")
    
    config = ImageJSCCConfig()
    
    # 计算编码器输出维度
    compressed_height = config.image_height // 8  # 32
    compressed_width = config.image_width // 8    # 64
    feature_dim = compressed_height * compressed_width  # 2048
    compressed_features = feature_dim // config.compression_ratio  # 2048 (compression_ratio=1)
    
    print(f"图像尺寸: {config.image_height}x{config.image_width}x{config.image_channels}")
    print(f"压缩后空间尺寸: {compressed_height}x{compressed_width}")
    print(f"特征维度: {feature_dim}")
    print(f"压缩比: {config.compression_ratio}")
    print(f"编码器输出维度: {compressed_features}")
    
    # 检查解码器期望的输入维度
    print(f"\n解码器CNN重建层期望:")
    print(f"  输入: [batch, {compressed_features}]")
    print(f"  第一层Dense输出: [batch, {compressed_height * compressed_width * 128}]")
    print(f"  Reshape后: [batch, {compressed_height}, {compressed_width}, 128]")
    
    return compressed_features

def test_encoder_decoder_separately():
    """分别测试编码器和解码器"""
    print("\n🧪 分别测试编码器和解码器...")
    
    config = ImageJSCCConfig()
    model = create_model(config)
    
    # 创建测试输入
    test_input = tf.random.uniform([2, config.image_height, config.image_width, config.image_channels])
    print(f"测试输入形状: {test_input.shape}")
    
    # 测试编码器
    print("\n📤 测试编码器...")
    encoded = model.encode(test_input)
    print(f"编码器输出形状: {encoded.shape}")
    print(f"编码器输出范围: [{tf.reduce_min(encoded):.3f}, {tf.reduce_max(encoded):.3f}]")
    print(f"编码器输出均值: {tf.reduce_mean(encoded):.3f}")
    print(f"编码器输出标准差: {tf.math.reduce_std(encoded):.3f}")

    # 测试解码器
    print("\n📥 测试解码器...")
    decoded = model.decode(encoded)
    print(f"解码器输出形状: {decoded.shape}")
    print(f"解码器输出范围: [{tf.reduce_min(decoded):.3f}, {tf.reduce_max(decoded):.3f}]")
    print(f"解码器输出均值: {tf.reduce_mean(decoded):.3f}")
    print(f"解码器输出标准差: {tf.math.reduce_std(decoded):.3f}")
    
    # 计算重建误差
    mse = tf.reduce_mean(tf.square(test_input - decoded))
    psnr = tf.reduce_mean(tf.image.psnr(test_input, decoded, max_val=1.0))
    
    print(f"\n📊 重建质量 (无信道噪声):")
    print(f"MSE: {mse:.6f}")
    print(f"PSNR: {psnr:.2f} dB")
    
    return encoded, decoded

def test_channel_effect():
    """测试信道对特征的影响"""
    print("\n📡 测试信道影响...")
    
    config = ImageJSCCConfig()
    model = create_model(config)
    
    # 创建测试输入
    test_input = tf.random.uniform([2, config.image_height, config.image_width, config.image_channels])
    
    # 编码
    encoded = model.encode(test_input)
    print(f"编码后特征: {encoded.shape}")
    print(f"编码特征范围: [{tf.reduce_min(encoded):.3f}, {tf.reduce_max(encoded):.3f}]")
    
    # 通过信道
    received = model.channel(encoded, snr_db=config.snr_dB, training=True)
    print(f"信道接收特征: {received.shape}")
    print(f"接收特征范围: [{tf.reduce_min(received):.3f}, {tf.reduce_max(received):.3f}]")
    
    # 计算信道引入的噪声
    noise_power = tf.reduce_mean(tf.square(encoded - received))
    signal_power = tf.reduce_mean(tf.square(encoded))
    actual_snr = 10 * tf.math.log(signal_power / (noise_power + 1e-8)) / tf.math.log(10.0)
    
    print(f"信号功率: {signal_power:.6f}")
    print(f"噪声功率: {noise_power:.6f}")
    print(f"实际SNR: {actual_snr:.2f} dB (设置: {config.snr_dB} dB)")
    
    # 解码
    decoded_clean = model.decode(encoded)
    decoded_noisy = model.decode(received)
    
    # 比较重建质量
    psnr_clean = tf.reduce_mean(tf.image.psnr(test_input, decoded_clean, max_val=1.0))
    psnr_noisy = tf.reduce_mean(tf.image.psnr(test_input, decoded_noisy, max_val=1.0))
    
    print(f"\n📊 重建质量对比:")
    print(f"无噪声PSNR: {psnr_clean:.2f} dB")
    print(f"有噪声PSNR: {psnr_noisy:.2f} dB")
    print(f"PSNR下降: {psnr_clean - psnr_noisy:.2f} dB")

def test_gradient_flow():
    """测试梯度流"""
    print("\n🌊 测试梯度流...")
    
    config = ImageJSCCConfig()
    model = create_model(config)
    
    # 创建测试输入
    test_input = tf.random.uniform([1, config.image_height, config.image_width, config.image_channels])
    
    with tf.GradientTape() as tape:
        output = model(test_input, training=True)
        loss = tf.reduce_mean(tf.square(test_input - output))
    
    # 计算梯度
    gradients = tape.gradient(loss, model.trainable_variables)
    
    # 检查梯度
    total_params = 0
    zero_grad_params = 0
    large_grad_params = 0
    
    for i, (var, grad) in enumerate(zip(model.trainable_variables, gradients)):
        if grad is not None:
            grad_norm = tf.norm(grad)
            param_count = tf.size(var).numpy()
            total_params += param_count
            
            if grad_norm < 1e-8:
                zero_grad_params += param_count
            elif grad_norm > 1.0:
                large_grad_params += param_count
                
            if i < 5:  # 只打印前5个层的信息
                print(f"层 {i}: {var.name}, 梯度范数: {grad_norm:.6f}, 参数数量: {param_count}")
    
    print(f"\n梯度统计:")
    print(f"总参数数: {total_params}")
    print(f"零梯度参数数: {zero_grad_params} ({zero_grad_params/total_params*100:.1f}%)")
    print(f"大梯度参数数: {large_grad_params} ({large_grad_params/total_params*100:.1f}%)")

def main():
    """主调试函数"""
    print("🚀 开始架构调试...")
    
    # 1. 检查特征维度
    compressed_features = debug_feature_dimensions()
    
    # 2. 分别测试编码器和解码器
    encoded, decoded = test_encoder_decoder_separately()
    
    # 3. 测试信道影响
    test_channel_effect()
    
    # 4. 测试梯度流
    test_gradient_flow()
    
    print("\n💡 可能的问题:")
    print("1. 如果编码器输出范围不在[0,1] → 检查激活函数")
    print("2. 如果解码器输出异常 → 检查维度匹配")
    print("3. 如果PSNR很低 → 检查网络容量和训练")
    print("4. 如果梯度为零 → 检查梯度流和激活函数")

if __name__ == "__main__":
    main()
