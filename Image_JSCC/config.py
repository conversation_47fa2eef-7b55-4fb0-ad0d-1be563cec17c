from datetime import datetime
import os

class ImageJSCCConfig:
    def __init__(self):
        # 数据参数
        self.image_height = 256    # 保持合理尺寸
        self.image_width = 512     # 保持合理尺寸
        self.image_channels = 3
        self.batch_size = 4       # 减小批次大小以适应更大的模型
        self.epochs = 300        # 大幅增加训练轮数以充分训练大模型
        self.test_interval = 2  # 更频繁的验证，便于监控
        
        # 数据路径 - 使用完整训练集
        self.data_root = "/home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train"  # 使用所有训练城市
        self.val_data_root = "/home/<USER>/GESCO/data/Cityscapes/leftImg8bit/val"  # 会自动搜索子目录
        
        # 优化器参数
        self.optimizer = "Adam"
        self.initial_lr = 0.00005   # 提高初始学习率，加快收敛
        self.lr_decay_strategy = "CosineAnnealing"  # 改为余弦退火，更平滑
        self.lr_decay_epochs = 20
        self.exp_decay_rate = 0.9   # 更激进的衰减率
        self.min_lr = 1e-6          # 最小学习率
        self.warmup_epochs = 5      # 预热轮数

        # 训练稳定性参数
        self.gradient_clip_norm = 1.0  # 梯度裁剪
        self.early_stopping_patience = 20  # 早停耐心值
        
        # 模型结构参数 - 平衡性能和内存使用
        # Transformer 编码器/解码器
        self.enc_TF_layers = 4   # 适中的层数
        self.dec_TF_layers = 4   # 适中的层数
        self.TF_heads = 8        # 保持注意力头数
        self.embedding_dim = 256 # 保持嵌入维度
        
        # 压缩相关参数 - 优化重建质量
        self.compression_ratio = 2   # 进一步减少压缩，1024维特征
        self.quantization_bits = 6   # 适中的量化精度
        self.feedback_bits = 16384   # 充足的传输容量，但不过度
        
        # 信道参数
        self.snr_dB = 10  # 信噪比 (dB)
        self.channel_type = "AWGN"  # "AWGN" or "CDL"
        
        # OFDM/信道参数
        self.num_ofdm_symbols = 1
        self.fft_size = 64
        self.num_subcarriers = self.fft_size
        self.subcarrier_spacing = 15e3  # Hz
        self.carrier_freq = 3.5e9       # Hz
        self.delay_spread = 100e-9      # 秒
        self.cp_length = 16
        self.num_bits_per_symbol = 2    # QPSK
        
        # 路径设置
        self.currentDT = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.root = "/home/<USER>/GESCO/Image_JSCC"
        self.ckpt_dir = f"{self.root}/checkpoints/ImageJSCC_snr_{self.snr_dB}dB_comp_{self.compression_ratio}_{self.currentDT}/"
        self.log_dir = f"{self.root}/logs/ImageJSCC_snr_{self.snr_dB}dB_comp_{self.compression_ratio}_{self.currentDT}/"
        
        # 创建目录
        os.makedirs(self.ckpt_dir, exist_ok=True)
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 说明备注
        self.notes = f"Image JSCC transmission with compression ratio {self.compression_ratio}, SNR {self.snr_dB}dB"
