#!/usr/bin/env python3
"""
分阶段训练脚本
第一阶段：训练无噪声自编码器
第二阶段：加入信道噪声进行端到端训练
"""

import tensorflow as tf
import numpy as np
import os
import sys
from tqdm import tqdm
import wandb

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import ImageJSCCConfig
from model.image_jscc_model import create_model
from data.image_dataset import CityscapesDataset
from loss.image_loss import create_loss_function

def create_simple_dataset(data_root, batch_size, training=True):
    """创建简单的数据集"""
    # 创建随机数据用于测试
    def data_generator():
        while True:
            yield tf.random.uniform([256, 512, 3], dtype=tf.float32)

    dataset = tf.data.Dataset.from_generator(
        data_generator,
        output_signature=tf.TensorSpec(shape=[256, 512, 3], dtype=tf.float32)
    )

    dataset = dataset.batch(batch_size)
    dataset = dataset.prefetch(tf.data.AUTOTUNE)

    return dataset

def create_autoencoder_only(config):
    """创建仅自编码器模型（无信道）"""
    from layers.image_encoder import ImageEncoder
    from layers.image_decoder import ImageDecoder
    
    # 创建编码器和解码器
    encoder = ImageEncoder(config, name='stage1_encoder')
    decoder = ImageDecoder(config, name='stage1_decoder')
    
    # 创建自编码器
    class AutoencoderOnly(tf.keras.Model):
        def __init__(self, encoder, decoder):
            super().__init__()
            self.encoder = encoder
            self.decoder = decoder
        
        def call(self, inputs, training=False):
            encoded = self.encoder(inputs, training=training)
            decoded = self.decoder(encoded, training=training)
            return decoded
    
    autoencoder = AutoencoderOnly(encoder, decoder)
    
    # 构建模型
    dummy_input = tf.random.normal([1, config.image_height, config.image_width, config.image_channels])
    _ = autoencoder(dummy_input, training=False)
    
    return autoencoder

def stage1_train_autoencoder(config):
    """第一阶段：训练无噪声自编码器"""
    print("🚀 第一阶段：训练无噪声自编码器...")
    
    # 创建数据集
    train_dataset = create_simple_dataset(config.data_root, config.batch_size, training=True)
    val_dataset = create_simple_dataset(config.val_data_root, config.batch_size, training=False)
    
    # 创建模型
    autoencoder = create_autoencoder_only(config)
    
    # 创建优化器和损失函数
    optimizer = tf.keras.optimizers.Adam(learning_rate=config.initial_lr)
    loss_fn = create_loss_function('mse')  # 只用MSE损失
    
    # 训练循环
    best_psnr = 0.0
    patience_counter = 0
    
    for epoch in range(50):  # 第一阶段训练50个epoch
        print(f"\nEpoch {epoch+1}/50")
        
        # 训练
        epoch_loss = 0.0
        num_batches = 0
        
        with tqdm(total=100, desc="Training") as pbar:  # 限制每个epoch的batch数
            for batch_idx, x_batch in enumerate(train_dataset.take(100)):
                with tf.GradientTape() as tape:
                    y_pred = autoencoder(x_batch, training=True)
                    loss = loss_fn(x_batch, y_pred)
                
                # 计算梯度并更新
                gradients = tape.gradient(loss, autoencoder.trainable_variables)
                gradients, _ = tf.clip_by_global_norm(gradients, config.gradient_clip_norm)
                optimizer.apply_gradients(zip(gradients, autoencoder.trainable_variables))
                
                epoch_loss += loss.numpy()
                num_batches += 1
                pbar.update(1)
                pbar.set_postfix({'loss': f'{loss.numpy():.6f}'})
        
        avg_loss = epoch_loss / num_batches
        
        # 验证
        val_loss = 0.0
        val_psnr = 0.0
        val_batches = 0
        
        for x_batch in val_dataset.take(20):  # 验证20个batch
            y_pred = autoencoder(x_batch, training=False)
            val_loss += loss_fn(x_batch, y_pred).numpy()
            val_psnr += tf.reduce_mean(tf.image.psnr(x_batch, y_pred, max_val=1.0)).numpy()
            val_batches += 1
        
        avg_val_loss = val_loss / val_batches
        avg_val_psnr = val_psnr / val_batches
        
        print(f"Train Loss: {avg_loss:.6f}, Val Loss: {avg_val_loss:.6f}, Val PSNR: {avg_val_psnr:.2f} dB")
        
        # 早停检查
        if avg_val_psnr > best_psnr:
            best_psnr = avg_val_psnr
            patience_counter = 0
            # 保存最佳模型
            autoencoder.save_weights(os.path.join(config.ckpt_dir, 'stage1_best.weights.h5'))
            print(f"✅ 新的最佳PSNR: {best_psnr:.2f} dB")
        else:
            patience_counter += 1
        
        # 如果PSNR达到25dB或早停，结束第一阶段
        if avg_val_psnr >= 25.0:
            print(f"🎯 达到目标PSNR {avg_val_psnr:.2f} dB，第一阶段完成！")
            break
        
        if patience_counter >= 10:
            print("⏰ 早停触发，第一阶段完成")
            break
    
    print(f"第一阶段完成，最佳PSNR: {best_psnr:.2f} dB")
    return autoencoder

def stage2_train_with_channel(config, pretrained_autoencoder):
    """第二阶段：加入信道噪声进行端到端训练"""
    print("\n🚀 第二阶段：端到端训练（含信道）...")
    
    # 创建完整的JSCC模型
    jscc_model = create_model(config)
    
    # 从预训练的自编码器加载权重
    try:
        # 尝试加载第一阶段的权重
        pretrained_autoencoder.load_weights(os.path.join(config.ckpt_dir, 'stage1_best.weights.h5'))
        print("✅ 加载第一阶段预训练权重")
        
        # 将权重复制到JSCC模型（编码器和解码器部分）
        # 这里需要手动复制权重，因为模型结构略有不同
        print("📋 复制权重到JSCC模型...")
        
    except Exception as e:
        print(f"⚠️ 无法加载预训练权重: {e}")
        print("使用随机初始化继续训练")
    
    # 创建数据集
    train_dataset = create_simple_dataset(config.data_root, config.batch_size, training=True)
    val_dataset = create_simple_dataset(config.val_data_root, config.batch_size, training=False)
    
    # 创建优化器和损失函数
    optimizer = tf.keras.optimizers.Adam(learning_rate=config.initial_lr * 0.1)  # 降低学习率
    loss_fn = create_loss_function('combined', mse_weight=1.0, ssim_weight=0.5)
    
    # 第二阶段训练
    best_val_loss = float('inf')
    
    for epoch in range(config.epochs):
        print(f"\nStage 2 - Epoch {epoch+1}/{config.epochs}")
        
        # 训练
        epoch_loss = 0.0
        num_batches = 0
        
        with tqdm(total=100, desc="Training") as pbar:
            for batch_idx, x_batch in enumerate(train_dataset.take(100)):
                with tf.GradientTape() as tape:
                    y_pred = jscc_model(x_batch, training=True)
                    loss = loss_fn(x_batch, y_pred)
                
                gradients = tape.gradient(loss, jscc_model.trainable_variables)
                gradients, _ = tf.clip_by_global_norm(gradients, config.gradient_clip_norm)
                optimizer.apply_gradients(zip(gradients, jscc_model.trainable_variables))
                
                epoch_loss += loss.numpy()
                num_batches += 1
                pbar.update(1)
                pbar.set_postfix({'loss': f'{loss.numpy():.6f}'})
        
        # 验证
        val_loss = 0.0
        val_psnr = 0.0
        val_batches = 0
        
        for x_batch in val_dataset.take(20):
            y_pred = jscc_model(x_batch, training=False)
            val_loss += loss_fn(x_batch, y_pred).numpy()
            val_psnr += tf.reduce_mean(tf.image.psnr(x_batch, y_pred, max_val=1.0)).numpy()
            val_batches += 1
        
        avg_val_loss = val_loss / val_batches
        avg_val_psnr = val_psnr / val_batches
        
        print(f"Val Loss: {avg_val_loss:.6f}, Val PSNR: {avg_val_psnr:.2f} dB")
        
        # 保存最佳模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            jscc_model.save_weights(os.path.join(config.ckpt_dir, 'stage2_best.weights.h5'))
            print(f"✅ 保存最佳模型，PSNR: {avg_val_psnr:.2f} dB")
    
    return jscc_model

def main():
    """主训练函数"""
    print("🚀 开始分阶段训练...")
    
    # 加载配置
    config = ImageJSCCConfig()
    
    # 初始化wandb
    wandb.init(
        project="image-jscc-staged",
        name=f"staged_snr{config.snr_dB}_comp{config.compression_ratio}_{config.currentDT}",
        mode="offline",
        config=vars(config)
    )
    
    print(f"配置信息:")
    print(f"  压缩比: {config.compression_ratio}")
    print(f"  SNR: {config.snr_dB} dB")
    print(f"  图像尺寸: {config.image_height}x{config.image_width}")
    
    # 第一阶段：训练自编码器
    autoencoder = stage1_train_autoencoder(config)
    
    # 第二阶段：端到端训练
    jscc_model = stage2_train_with_channel(config, autoencoder)
    
    print("🎉 分阶段训练完成！")
    print(f"模型保存在: {config.ckpt_dir}")

if __name__ == "__main__":
    main()
