#!/usr/bin/env python3
"""
修复网络架构的建议和实现
"""

import tensorflow as tf
import numpy as np
import sys
import os

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_compression_requirements():
    """分析压缩需求"""
    print("🔍 分析图像压缩需求...")
    
    # 原始图像信息
    height, width, channels = 256, 512, 3
    total_pixels = height * width * channels
    
    print(f"原始图像: {height}×{width}×{channels} = {total_pixels:,} 像素")
    print(f"原始数据量: {total_pixels * 4 / 1024 / 1024:.1f} MB (float32)")
    
    # 分析不同压缩比的特征维度
    compression_ratios = [1, 2, 4, 8, 16, 32, 64]
    
    print(f"\n压缩比分析:")
    for ratio in compression_ratios:
        # 计算CNN下采样后的特征维度
        feature_h = height // 8  # 3次stride=2下采样
        feature_w = width // 8
        feature_dim = feature_h * feature_w  # 2048
        
        compressed_dim = feature_dim // ratio
        compression_rate = total_pixels / compressed_dim
        
        print(f"  压缩比 {ratio:2d}: {compressed_dim:4d} 特征 -> 实际压缩 {compression_rate:.1f}x")
        
        # 估算重建质量
        if compression_rate > 1000:
            quality = "极差"
        elif compression_rate > 500:
            quality = "很差"
        elif compression_rate > 200:
            quality = "较差"
        elif compression_rate > 100:
            quality = "一般"
        elif compression_rate > 50:
            quality = "较好"
        else:
            quality = "很好"
        
        print(f"                预期质量: {quality}")

def create_improved_encoder(config):
    """创建改进的编码器"""
    print("\n🔧 创建改进的编码器...")
    
    # 使用更合理的压缩比
    feature_h = config.image_height // 8
    feature_w = config.image_width // 8
    feature_dim = feature_h * feature_w
    
    # 设置更合理的压缩维度 (压缩比4-8比较合适)
    compressed_dim = feature_dim // 4  # 512维，压缩比4
    
    encoder = tf.keras.Sequential([
        # 输入层
        tf.keras.layers.Input(shape=(config.image_height, config.image_width, config.image_channels)),
        
        # 第一个卷积块 (256x512 -> 128x256)
        tf.keras.layers.Conv2D(64, 3, strides=2, padding='same'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.ReLU(),
        
        # 第二个卷积块 (128x256 -> 64x128)
        tf.keras.layers.Conv2D(128, 3, strides=2, padding='same'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.ReLU(),
        
        # 第三个卷积块 (64x128 -> 32x64)
        tf.keras.layers.Conv2D(256, 3, strides=2, padding='same'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.ReLU(),
        
        # 全局平均池化 + 压缩
        tf.keras.layers.GlobalAveragePooling2D(),
        tf.keras.layers.Dense(512, activation='relu'),
        tf.keras.layers.Dense(compressed_dim, activation='tanh'),  # 输出到[-1,1]
    ], name='improved_encoder')
    
    print(f"编码器输出维度: {compressed_dim}")
    return encoder, compressed_dim

def create_improved_decoder(config, compressed_dim):
    """创建改进的解码器"""
    print("🔧 创建改进的解码器...")
    
    # 计算重建起始维度
    start_h = config.image_height // 8  # 32
    start_w = config.image_width // 8   # 64
    start_c = 256
    
    decoder = tf.keras.Sequential([
        # 输入层
        tf.keras.layers.Input(shape=(compressed_dim,)),
        
        # 特征重建
        tf.keras.layers.Dense(512, activation='relu'),
        tf.keras.layers.Dense(start_h * start_w * start_c, activation='relu'),
        tf.keras.layers.Reshape((start_h, start_w, start_c)),
        
        # 第一个上采样块 (32x64 -> 64x128)
        tf.keras.layers.Conv2DTranspose(128, 3, strides=2, padding='same'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.ReLU(),
        
        # 第二个上采样块 (64x128 -> 128x256)
        tf.keras.layers.Conv2DTranspose(64, 3, strides=2, padding='same'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.ReLU(),
        
        # 第三个上采样块 (128x256 -> 256x512)
        tf.keras.layers.Conv2DTranspose(32, 3, strides=2, padding='same'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.ReLU(),
        
        # 输出层
        tf.keras.layers.Conv2D(config.image_channels, 3, padding='same', activation='sigmoid'),
    ], name='improved_decoder')
    
    return decoder

def test_improved_architecture():
    """测试改进的架构"""
    print("\n🧪 测试改进的架构...")
    
    from config import ImageJSCCConfig
    config = ImageJSCCConfig()
    
    # 创建改进的编码器和解码器
    encoder, compressed_dim = create_improved_encoder(config)
    decoder = create_improved_decoder(config, compressed_dim)
    
    # 创建完整的自编码器
    autoencoder = tf.keras.Sequential([encoder, decoder], name='improved_autoencoder')
    
    # 测试
    test_input = tf.random.uniform([2, config.image_height, config.image_width, config.image_channels])
    
    print(f"测试输入: {test_input.shape}")
    
    # 编码
    encoded = encoder(test_input)
    print(f"编码输出: {encoded.shape}")
    print(f"编码范围: [{tf.reduce_min(encoded):.3f}, {tf.reduce_max(encoded):.3f}]")
    print(f"编码标准差: {tf.math.reduce_std(encoded):.3f}")
    
    # 解码
    decoded = decoder(encoded)
    print(f"解码输出: {decoded.shape}")
    print(f"解码范围: [{tf.reduce_min(decoded):.3f}, {tf.reduce_max(decoded):.3f}]")
    print(f"解码标准差: {tf.math.reduce_std(decoded):.3f}")
    
    # 完整重建
    reconstructed = autoencoder(test_input)
    
    # 计算质量
    mse = tf.reduce_mean(tf.square(test_input - reconstructed))
    psnr = tf.reduce_mean(tf.image.psnr(test_input, reconstructed, max_val=1.0))
    
    print(f"\n重建质量 (随机权重):")
    print(f"  MSE: {mse:.6f}")
    print(f"  PSNR: {psnr:.2f} dB")
    
    # 计算压缩比
    original_size = config.image_height * config.image_width * config.image_channels
    compression_ratio = original_size / compressed_dim
    
    print(f"\n压缩信息:")
    print(f"  原始像素: {original_size:,}")
    print(f"  压缩特征: {compressed_dim}")
    print(f"  压缩比: {compression_ratio:.1f}x")
    
    return autoencoder, encoder, decoder

def suggest_architecture_fixes():
    """建议架构修复方案"""
    print("\n💡 架构修复建议:")
    print("1. 压缩比问题:")
    print("   - 当前: 128维特征重建393,216像素 (压缩比3072x)")
    print("   - 建议: 512维特征重建393,216像素 (压缩比768x)")
    print("   - 原因: 过度压缩导致信息丢失严重")
    
    print("\n2. 网络架构问题:")
    print("   - 当前: 复杂的Transformer + CNN混合架构")
    print("   - 建议: 简化为纯CNN自编码器")
    print("   - 原因: 减少复杂度，提高训练稳定性")
    
    print("\n3. 激活函数问题:")
    print("   - 当前: sigmoid输出限制在[0,1]")
    print("   - 建议: tanh输出范围[-1,1]，增加表达能力")
    print("   - 原因: 更大的输出范围有助于特征表达")
    
    print("\n4. 信道实现问题:")
    print("   - 当前: BPSK调制假设输入在[0,1]")
    print("   - 建议: 修改信道以适应[-1,1]输入")
    print("   - 原因: 与tanh输出匹配")
    
    print("\n5. 训练策略问题:")
    print("   - 建议: 先训练无噪声自编码器")
    print("   - 然后: 逐步增加信道噪声")
    print("   - 原因: 分阶段训练更稳定")

def main():
    """主函数"""
    print("🚀 开始架构分析和修复...")
    
    # 1. 分析压缩需求
    analyze_compression_requirements()
    
    # 2. 测试改进的架构
    autoencoder, encoder, decoder = test_improved_architecture()
    
    # 3. 提供修复建议
    suggest_architecture_fixes()
    
    print("\n✅ 分析完成！")
    print("\n下一步:")
    print("1. 修改config.py中的compression_ratio为4")
    print("2. 简化编码器和解码器架构")
    print("3. 修复信道实现以支持[-1,1]输入")
    print("4. 采用分阶段训练策略")

if __name__ == "__main__":
    main()
