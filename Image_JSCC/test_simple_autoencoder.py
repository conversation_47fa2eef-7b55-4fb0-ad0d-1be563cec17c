#!/usr/bin/env python3
"""
测试简单的自编码器功能
"""

import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import sys
import os

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_simple_autoencoder(input_shape, compressed_dim):
    """创建简单的自编码器"""
    
    # 编码器
    encoder = tf.keras.Sequential([
        tf.keras.layers.Input(shape=input_shape),
        tf.keras.layers.Conv2D(64, 3, strides=2, padding='same', activation='relu'),
        tf.keras.layers.Conv2D(128, 3, strides=2, padding='same', activation='relu'),
        tf.keras.layers.Conv2D(256, 3, strides=2, padding='same', activation='relu'),
        tf.keras.layers.Flatten(),
        tf.keras.layers.Dense(compressed_dim, activation='sigmoid')
    ], name='simple_encoder')
    
    # 计算解码器的起始维度
    # 输入: 256x512x3 -> 经过3次stride=2下采样 -> 32x64x256
    decoder_start_h = input_shape[0] // 8
    decoder_start_w = input_shape[1] // 8
    decoder_start_c = 256
    
    # 解码器
    decoder = tf.keras.Sequential([
        tf.keras.layers.Input(shape=(compressed_dim,)),
        tf.keras.layers.Dense(decoder_start_h * decoder_start_w * decoder_start_c, activation='relu'),
        tf.keras.layers.Reshape((decoder_start_h, decoder_start_w, decoder_start_c)),
        tf.keras.layers.Conv2DTranspose(128, 3, strides=2, padding='same', activation='relu'),
        tf.keras.layers.Conv2DTranspose(64, 3, strides=2, padding='same', activation='relu'),
        tf.keras.layers.Conv2DTranspose(input_shape[2], 3, strides=2, padding='same', activation='sigmoid')
    ], name='simple_decoder')
    
    # 完整自编码器
    autoencoder = tf.keras.Sequential([encoder, decoder], name='simple_autoencoder')
    
    return autoencoder, encoder, decoder

def test_simple_autoencoder():
    """测试简单自编码器"""
    print("🧪 测试简单自编码器...")
    
    # 创建模型
    input_shape = (256, 512, 3)
    compressed_dim = 2048  # 与原始模型相同
    
    autoencoder, encoder, decoder = create_simple_autoencoder(input_shape, compressed_dim)
    
    # 编译模型
    autoencoder.compile(optimizer='adam', loss='mse', metrics=['mae'])
    
    # 创建测试数据
    test_input = tf.random.uniform([4, *input_shape])
    print(f"测试输入形状: {test_input.shape}")
    print(f"测试输入范围: [{tf.reduce_min(test_input):.3f}, {tf.reduce_max(test_input):.3f}]")
    
    # 测试前向传播
    encoded = encoder(test_input)
    decoded = decoder(encoded)
    reconstructed = autoencoder(test_input)
    
    print(f"\n编码器输出:")
    print(f"  形状: {encoded.shape}")
    print(f"  范围: [{tf.reduce_min(encoded):.3f}, {tf.reduce_max(encoded):.3f}]")
    print(f"  均值: {tf.reduce_mean(encoded):.3f}")
    print(f"  标准差: {tf.math.reduce_std(encoded):.3f}")
    
    print(f"\n解码器输出:")
    print(f"  形状: {decoded.shape}")
    print(f"  范围: [{tf.reduce_min(decoded):.3f}, {tf.reduce_max(decoded):.3f}]")
    print(f"  均值: {tf.reduce_mean(decoded):.3f}")
    print(f"  标准差: {tf.math.reduce_std(decoded):.3f}")
    
    # 计算重建质量
    mse = tf.reduce_mean(tf.square(test_input - reconstructed))
    psnr = tf.reduce_mean(tf.image.psnr(test_input, reconstructed, max_val=1.0))
    
    print(f"\n重建质量 (随机权重):")
    print(f"  MSE: {mse:.6f}")
    print(f"  PSNR: {psnr:.2f} dB")
    
    # 简单训练几步
    print(f"\n🏋️ 简单训练...")
    
    # 创建更多训练数据
    train_data = tf.random.uniform([32, *input_shape])
    
    # 训练几个epoch
    history = autoencoder.fit(train_data, train_data, 
                             epochs=10, 
                             batch_size=4, 
                             verbose=1)
    
    # 重新测试
    reconstructed_trained = autoencoder(test_input)
    mse_trained = tf.reduce_mean(tf.square(test_input - reconstructed_trained))
    psnr_trained = tf.reduce_mean(tf.image.psnr(test_input, reconstructed_trained, max_val=1.0))
    
    print(f"\n重建质量 (训练后):")
    print(f"  MSE: {mse_trained:.6f}")
    print(f"  PSNR: {psnr_trained:.2f} dB")
    print(f"  PSNR提升: {psnr_trained - psnr:.2f} dB")
    
    return autoencoder, encoder, decoder

def test_with_real_image():
    """使用真实图像测试"""
    print("\n🖼️ 使用真实图像测试...")
    
    # 创建简单的测试图像
    img = np.random.rand(256, 512, 3).astype(np.float32)
    img_batch = np.expand_dims(img, axis=0)
    
    # 创建模型
    input_shape = (256, 512, 3)
    compressed_dim = 2048
    
    autoencoder, encoder, decoder = create_simple_autoencoder(input_shape, compressed_dim)
    autoencoder.compile(optimizer='adam', loss='mse')
    
    # 训练
    print("训练自编码器...")
    autoencoder.fit(img_batch, img_batch, epochs=50, verbose=0)
    
    # 测试重建
    reconstructed = autoencoder(img_batch)
    
    mse = tf.reduce_mean(tf.square(img_batch - reconstructed))
    psnr = tf.reduce_mean(tf.image.psnr(img_batch, reconstructed, max_val=1.0))
    
    print(f"真实图像重建:")
    print(f"  MSE: {mse:.6f}")
    print(f"  PSNR: {psnr:.2f} dB")
    
    # 保存对比图像
    fig, axes = plt.subplots(1, 2, figsize=(10, 5))
    axes[0].imshow(img)
    axes[0].set_title('Original')
    axes[0].axis('off')
    
    axes[1].imshow(np.clip(reconstructed[0].numpy(), 0, 1))
    axes[1].set_title(f'Reconstructed (PSNR: {psnr:.1f} dB)')
    axes[1].axis('off')
    
    plt.tight_layout()
    plt.savefig('simple_autoencoder_test.png', dpi=150, bbox_inches='tight')
    print("对比图像已保存为 simple_autoencoder_test.png")

def main():
    """主函数"""
    print("🚀 开始简单自编码器测试...")
    
    # 测试简单自编码器
    autoencoder, encoder, decoder = test_simple_autoencoder()
    
    # 使用真实图像测试
    test_with_real_image()
    
    print("\n✅ 测试完成！")

if __name__ == "__main__":
    main()
