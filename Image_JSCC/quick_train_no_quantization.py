#!/usr/bin/env python3
"""
快速训练去掉量化的模型
"""

import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import os
from tqdm import tqdm

# 设置GPU
os.environ['CUDA_VISIBLE_DEVICES'] = '1'
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
    except RuntimeError as e:
        print(e)

from config import ImageJSCCConfig
from model.image_jscc_model import create_model
from data.image_dataset import get_image_dataset
from loss.image_loss import create_loss_function, ImageMetrics

def quick_train_no_quantization():
    """快速训练去掉量化的模型"""
    print("🚀 开始快速训练去掉量化的模型...")
    
    config = ImageJSCCConfig()
    config.batch_size = 4  # 适中的批次大小
    
    print(f"配置: {config.image_height}×{config.image_width}, batch={config.batch_size}")
    
    # 创建模型
    model = create_model(config)
    
    # 创建优化器和损失函数
    optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
    loss_fn = create_loss_function('mse')
    metrics_fn = ImageMetrics()
    
    # 创建数据集
    train_dataset = get_image_dataset(config, config.batch_size, dataset_type='train')
    
    # 获取一个固定的测试批次用于监控
    test_batch = None
    for batch in train_dataset.take(1):
        test_batch = batch
        break
    
    print(f"测试批次: {test_batch.shape}")
    
    # 训练前测试
    print("\n📊 训练前测试:")
    y_pred_before = model(test_batch, training=False)
    loss_before = loss_fn(test_batch, y_pred_before)
    metrics_before = metrics_fn(test_batch, y_pred_before)
    
    print(f"损失: {loss_before:.6f}")
    print(f"PSNR: {metrics_before['psnr']:.2f} dB")
    print(f"SSIM: {metrics_before['ssim']:.4f}")
    print(f"输出范围: [{tf.reduce_min(y_pred_before):.4f}, {tf.reduce_max(y_pred_before):.4f}]")
    
    # 训练循环
    print("\n🏋️ 开始训练...")
    
    num_epochs = 20
    steps_per_epoch = 50  # 每个epoch训练50步
    
    train_losses = []
    test_metrics = []
    
    for epoch in range(num_epochs):
        epoch_losses = []
        
        # 训练一个epoch
        train_iter = iter(train_dataset)
        for step in range(steps_per_epoch):
            try:
                x_batch = next(train_iter)
            except StopIteration:
                train_iter = iter(train_dataset)
                x_batch = next(train_iter)
            
            # 训练步骤
            with tf.GradientTape() as tape:
                y_pred = model(x_batch, training=True)
                loss = loss_fn(x_batch, y_pred)
            
            gradients = tape.gradient(loss, model.trainable_variables)
            optimizer.apply_gradients(zip(gradients, model.trainable_variables))
            
            epoch_losses.append(loss.numpy())
        
        avg_loss = np.mean(epoch_losses)
        train_losses.append(avg_loss)
        
        # 每5个epoch测试一次
        if epoch % 5 == 0:
            y_pred_test = model(test_batch, training=False)
            test_loss = loss_fn(test_batch, y_pred_test)
            test_metrics_epoch = metrics_fn(test_batch, y_pred_test)
            
            test_metrics.append({
                'epoch': epoch,
                'loss': test_loss.numpy(),
                'psnr': test_metrics_epoch['psnr'].numpy(),
                'ssim': test_metrics_epoch['ssim'].numpy(),
                'output_min': tf.reduce_min(y_pred_test).numpy(),
                'output_max': tf.reduce_max(y_pred_test).numpy()
            })
            
            print(f"Epoch {epoch+1:2d}: 训练损失={avg_loss:.6f}, "
                  f"测试损失={test_loss:.6f}, PSNR={test_metrics_epoch['psnr']:.2f}dB, "
                  f"输出范围=[{tf.reduce_min(y_pred_test):.3f}, {tf.reduce_max(y_pred_test):.3f}]")
    
    # 训练后测试
    print("\n📊 训练后测试:")
    y_pred_after = model(test_batch, training=False)
    loss_after = loss_fn(test_batch, y_pred_after)
    metrics_after = metrics_fn(test_batch, y_pred_after)
    
    print(f"损失: {loss_after:.6f}")
    print(f"PSNR: {metrics_after['psnr']:.2f} dB")
    print(f"SSIM: {metrics_after['ssim']:.4f}")
    print(f"输出范围: [{tf.reduce_min(y_pred_after):.4f}, {tf.reduce_max(y_pred_after):.4f}]")
    
    # 改善情况
    print(f"\n📈 改善情况:")
    print(f"损失: {loss_before:.6f} → {loss_after:.6f} (改善 {((loss_before-loss_after)/loss_before*100):.1f}%)")
    print(f"PSNR: {metrics_before['psnr']:.2f}dB → {metrics_after['psnr']:.2f}dB (改善 {(metrics_after['psnr']-metrics_before['psnr']):.1f}dB)")
    print(f"SSIM: {metrics_before['ssim']:.4f} → {metrics_after['ssim']:.4f} (改善 {((metrics_after['ssim']-metrics_before['ssim'])/metrics_before['ssim']*100):.1f}%)")
    
    # 可视化结果
    visualize_training_results(test_batch, y_pred_before, y_pred_after, 
                              metrics_before, metrics_after, train_losses, test_metrics)
    
    return model, test_batch, y_pred_after

def visualize_training_results(test_batch, y_pred_before, y_pred_after, 
                              metrics_before, metrics_after, train_losses, test_metrics):
    """可视化训练结果"""
    print("\n📊 生成训练结果图...")
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 原图
    original_img = np.clip(test_batch[0].numpy(), 0, 1)
    axes[0, 0].imshow(original_img)
    axes[0, 0].set_title('Original Image')
    axes[0, 0].axis('off')
    
    # 训练前
    before_img = np.clip(y_pred_before[0].numpy(), 0, 1)
    axes[0, 1].imshow(before_img)
    axes[0, 1].set_title(f'Before Training\nPSNR: {metrics_before["psnr"]:.2f}dB')
    axes[0, 1].axis('off')
    
    # 训练后
    after_img = np.clip(y_pred_after[0].numpy(), 0, 1)
    axes[0, 2].imshow(after_img)
    axes[0, 2].set_title(f'After Training\nPSNR: {metrics_after["psnr"]:.2f}dB')
    axes[0, 2].axis('off')
    
    # 训练损失曲线
    axes[1, 0].plot(train_losses)
    axes[1, 0].set_title('Training Loss')
    axes[1, 0].set_xlabel('Epoch')
    axes[1, 0].set_ylabel('Loss')
    axes[1, 0].grid(True)
    
    # PSNR曲线
    if test_metrics:
        epochs = [m['epoch'] for m in test_metrics]
        psnrs = [m['psnr'] for m in test_metrics]
        axes[1, 1].plot(epochs, psnrs, 'r-o')
        axes[1, 1].set_title('PSNR Progress')
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('PSNR (dB)')
        axes[1, 1].grid(True)
    
    # 输出范围变化
    if test_metrics:
        output_mins = [m['output_min'] for m in test_metrics]
        output_maxs = [m['output_max'] for m in test_metrics]
        axes[1, 2].plot(epochs, output_mins, 'b-o', label='Min')
        axes[1, 2].plot(epochs, output_maxs, 'r-o', label='Max')
        axes[1, 2].axhline(y=0.5, color='gray', linestyle='--', alpha=0.5, label='0.5 line')
        axes[1, 2].set_title('Output Range Progress')
        axes[1, 2].set_xlabel('Epoch')
        axes[1, 2].set_ylabel('Output Value')
        axes[1, 2].legend()
        axes[1, 2].grid(True)
    
    plt.tight_layout()
    plt.savefig('quick_train_no_quantization_results.png', dpi=150, bbox_inches='tight')
    plt.show()
    print("训练结果已保存为 'quick_train_no_quantization_results.png'")

def main():
    """主函数"""
    print("🎯 验证去掉量化后的模型训练效果...")
    
    try:
        model, test_batch, final_output = quick_train_no_quantization()
        
        print("\n💡 结论:")
        final_psnr = tf.reduce_mean(tf.image.psnr(test_batch, final_output, max_val=1.0))
        
        if final_psnr > 20:
            print("✅ 训练成功！去掉量化后模型能够正常学习")
            print("   建议：")
            print("   1. 使用这个配置进行完整训练")
            print("   2. 训练收敛后再考虑添加量化")
            print("   3. 量化可能确实是问题所在")
        elif final_psnr > 15:
            print("🔶 部分成功！模型有改善但还需要更多训练")
            print("   建议：")
            print("   1. 增加训练轮数")
            print("   2. 调整学习率")
            print("   3. 检查模型容量")
        else:
            print("❌ 训练效果不佳，可能还有其他问题")
            print("   需要进一步检查：")
            print("   1. 模型结构")
            print("   2. 损失函数")
            print("   3. 数据质量")
            
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
